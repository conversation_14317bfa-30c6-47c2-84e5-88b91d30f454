# Elasticsearch Search Architecture

A complete search solution consisting of a secure data synchronization pipeline and production-ready search abstraction with mandatory tenant isolation.

## 📚 Documentation Overview

### Search Repository (Query & Retrieval)
| Document | Audience | Purpose |
|----------|----------|---------|
| **[Repository Architecture](../../apps/platform-api/src/repositories/external/Elasticsearch/README.md)** | Technical Leadership, Architects | Search query design, security model, performance characteristics |
| **[Repository API Reference](../../apps/platform-api/src/repositories/external/Elasticsearch/api.md)** | Developers | Search examples, configuration options, best practices |
| **[Repository Source Code](../../apps/platform-api/src/repositories/external/Elasticsearch/ElasticsearchRepository.ts)** | Developers | Implementation with inline JSDoc |

### Data Synchronization Pipeline (Data Ingestion)
| Document | Audience | Purpose |
|----------|----------|---------|
| **[Sync Service README](../../apps/elasticsearch-sync/README.md)** | DevOps, Platform Engineers | Data pipeline setup, performance tuning, monitoring |
| **[Sync Architecture](../../apps/elasticsearch-sync/docs/architecure.md)** | Technical Leadership, Architects | Data flow design, component architecture, scalability |
| **[Local Development](../../apps/elasticsearch-sync/LOCAL_ELASTICSEARCH.md)** | Developers | Local setup and testing |
| **[Schema Management](../../apps/elasticsearch-sync/scripts/schema-reindex.md)** | DevOps, Data Engineers | Index schema updates and reindexing |

## 🚀 Quick Start

### Search Usage (Most Common)
```typescript
import { ElasticsearchRepository } from '@repositories/external/Elasticsearch/ElasticsearchRepository';

// Basic setup
const repository = new ElasticsearchRepository({
  client: elasticsearchClient,
  transformer: searchResultTransformer
});

// Simple search with automatic tenant isolation
const results = await repository.search(adminToken, {
  input: { 
    index: 'cases_applications', 
    search: 'emergency assistance' 
  }
});
```

### Data Pipeline Setup (Platform/DevOps)
```bash
# Set up local development
cd apps/elasticsearch-sync
python -m pip install -r requirements.txt

# Configure subscriptions
export ELASTICSEARCH_SYNC_CONFIG='{
  "subscriptions": {
    "cases-topic": {
      "id_fields": ["case_id", "application_id"],
      "doc_id_template": "case_{case_id}_app_{application_id}",
      "query": "cases_applications_query",
      "es_index": "cases_applications"
    }
  }
}'

# Run synchronization service
python -m src.main
```

## 🔒 Security Highlights

- **Mandatory tenant isolation** enforced at compile-time and runtime
- **Partner filtering** required for all index implementations  
- **Secure query structure** with security filters always applied first
- **No bypass mechanisms** - security failures bubble up immediately

## ⚡ Performance Features

- **Cursor-based pagination** using `search_after` for efficient large dataset traversal
- **Optional Point-in-Time (PIT)** contexts for consistent pagination
- **Category-specific searches** for better performance than global text search
- **Configurable page sizes** and resource limits

## 🏗️ Complete Search Architecture

```mermaid
graph TD
    subgraph "Data Sources"
        DB[(PostgreSQL Database)]
        PS[Google Cloud PubSub]
    end
    
    subgraph "Data Synchronization Pipeline"
        PS --> SM[Subscription Manager]
        SM --> RA[Raw Accumulator]
        RA --> RP[Raw Processor]
        RP --> BP[Batch Processor]
        BP --> DB
        BP --> MA[Message Accumulator]
        MA --> WM[Worker Manager]
        WM --> MP[Message Processor]
    end
    
    subgraph "Elasticsearch Cluster"
        MP --> ES[(Elasticsearch)]
    end
    
    subgraph "Search Repository"
        CLIENT[Client Applications] --> REPO[ElasticsearchRepository]
        REPO --> FB[FilterBuilder<br/>Security + Filters]
        REPO --> SB[SearchBuilder<br/>Text Search]
        REPO --> SORT[SortBuilder<br/>Sorting Logic]
        REPO --> TRANS[SearchResultTransformer]
        REPO --> ES
    end
    
    style ES fill:#f9f9f9
    style REPO fill:#e8f5e8
    style FB fill:#ffebee
    style SM fill:#e3f2fd
```

### Data Flow
1. **Data Changes** → PubSub messages trigger sync pipeline
2. **Sync Pipeline** → Transforms and indexes data into Elasticsearch  
3. **Search Repository** → Provides secure, typed search interface
4. **Client Applications** → Get real-time search results with tenant isolation

The architecture ensures:
- **Real-time sync**: Data changes are reflected in search within seconds
- **High performance**: Optimized batch processing and concurrent workers
- **Security**: Mandatory tenant isolation at query time
- **Scalability**: Horizontal scaling of both sync and search components

## 📖 What Each Document Covers

### [Architecture & Design README](../../apps/platform-api/src/repositories/external/Elasticsearch/README.md)
**For: Technical Leadership, Architects, New Team Members**

- 🏛️ **Design Principles**: Security-first, index-agnostic, performance-conscious
- 🔐 **Security Model**: Tenant isolation strategy and enforcement mechanisms  
- 📊 **Performance Analysis**: Pagination strategies, PIT trade-offs, query costs
- 🛠️ **Extension Guide**: How to add support for new indices
- 📈 **Monitoring**: Key metrics and observability recommendations
- 🔄 **Migration Guide**: Moving from direct Elasticsearch usage

### [API Reference & Usage](../../apps/platform-api/src/repositories/external/Elasticsearch/api.md)
**For: Developers Building Features**

- 🚀 **Quick Start**: Basic setup and simple examples
- 🔍 **Search Patterns**: Text search, category search, filtering, sorting
- 📄 **Pagination**: Forward/backward navigation, large datasets, cursor management
- ⚙️ **Configuration**: Environment-specific settings, performance tuning
- ❌ **Error Handling**: Graceful degradation, validation, troubleshooting
- 🏎️ **Performance**: Optimization strategies, best practices, monitoring
- 🔧 **Advanced Patterns**: Builder pattern, batch operations, caching

### [Source Code with JSDoc](../../apps/platform-api/src/repositories/external/Elasticsearch/ElasticsearchRepository.ts)
**For: Developers Working on Implementation**

- 💻 **Method Documentation**: Parameter descriptions, return types, examples
- 🔧 **Implementation Details**: How methods work internally
- 📝 **Inline Examples**: Code snippets visible in IDE
- ⚠️ **Error Conditions**: What throws exceptions and when
- 🔗 **Cross-References**: Links to comprehensive documentation

## 🎯 Choose Your Path

### I'm a Developer Building Search Features
👉 Start with **[Repository API Reference](../../apps/platform-api/src/repositories/external/Elasticsearch/api.md)** for search examples and configuration

### I'm Setting Up Data Synchronization
👉 Start with **[Sync Service README](../../apps/elasticsearch-sync/README.md)** for pipeline setup and configuration

### I'm Technical Leadership Reviewing Architecture  
👉 Review both **[Repository Architecture](../../apps/platform-api/src/repositories/external/Elasticsearch/README.md)** and **[Sync Architecture](../../apps/elasticsearch-sync/docs/architecure.md)** for complete system design

### I'm Working on Search Implementation
👉 Start with **[Repository Source Code](../../apps/platform-api/src/repositories/external/Elasticsearch/ElasticsearchRepository.ts)** for method-level documentation

### I'm Working on Data Pipeline
👉 Start with **[Sync Service Source](../../apps/elasticsearch-sync/src/)** and **[Architecture Docs](../../apps/elasticsearch-sync/docs/architecure.md)**

### I'm Adding Support for a New Index
👉 Follow the **[Repository Extension Guide](../../apps/platform-api/src/repositories/external/Elasticsearch/README.md#extending-for-new-indices)** AND configure sync in **[Sync Service](../../apps/elasticsearch-sync/README.md)**

### I'm Troubleshooting Search Performance
👉 Check **[Repository Performance Guide](../../apps/platform-api/src/repositories/external/Elasticsearch/api.md#performance-optimization)**

### I'm Troubleshooting Data Pipeline Performance  
👉 Check **[Sync Service Performance Monitoring](../../apps/elasticsearch-sync/README.md)** and metrics API

### I'm Managing Schema Changes
👉 Follow **[Schema Reindexing Guide](../../apps/elasticsearch-sync/scripts/schema-reindex.md)**

## 🔧 Related Components

### Search Repository Components
- **[FilterBuilder](../../apps/platform-api/src/repositories/external/Elasticsearch/filters/)** - Index-specific filtering and security
- **[SearchBuilder](../../apps/platform-api/src/repositories/external/Elasticsearch/search/)** - Text search and category handling  
- **[SortBuilder](../../apps/platform-api/src/repositories/external/Elasticsearch/sorts/)** - Column sorting and defaults
- **[SearchResultTransformer](../../apps/platform-api/src/repositories/external/Elasticsearch/transformer/)** - Response formatting

### Data Synchronization Components  
- **[Elasticsearch Synchronizer](../../apps/elasticsearch-sync/src/core/elasticsearch_synchronizer.py)** - Main orchestrator
- **[Subscription Manager](../../apps/elasticsearch-sync/src/core/subscription_manager.py)** - Per-subscription pipeline management
- **[Batch Processor](../../apps/elasticsearch-sync/src/core/batch_processor.py)** - PostgreSQL data fetching
- **[Message Processor](../../apps/elasticsearch-sync/src/core/message_processor.py)** - Data transformation
- **[Worker Manager](../../apps/elasticsearch-sync/src/core/worker_manager.py)** - Parallel processing coordination
- **[Performance Monitor](../../apps/elasticsearch-sync/src/core/performance_monitor.py)** - Metrics and monitoring

## 📊 Current Index Support

| Index | Sync Pipeline | FilterBuilder | SearchBuilder | SortBuilder | Status |
|-------|---------------|---------------|---------------|-------------|---------|
| `cases_applications` | ✅ | ✅ | ✅ | ✅ | Production Ready |
| *Add new indices here* | | | | | |

### Adding a New Index
To support a new index, you need to configure **both** systems:

1. **Data Sync Pipeline**:
   - Add subscription configuration in elasticsearch-sync
   - Configure ID fields, document templates, and queries
   - Set up PubSub subscription for data changes

2. **Search Repository**:
   - Implement FilterBuilder (required for security)
   - Implement SearchBuilder (optional, for text search)
   - Implement SortBuilder (optional, for sorting)

## 🆘 Need Help?

1. **Check the appropriate documentation** using the guide above
2. **Search existing issues** in the repository  
3. **Create a new issue** with:
   - Which document you consulted
   - What you're trying to achieve
   - Error messages or unexpected behavior
   - Code snippets (sanitized of sensitive data)

---

*This documentation was created using a hybrid approach: minimal JSDoc for IDE integration, comprehensive README for architecture, and detailed API guide for implementation. Each serves its specific audience while cross-referencing for complete coverage.*
interface ServiceConfig {
  name: string;
  ports: {
    local: number;
    docker: number;
    gateway?: number;
  };
}

// Port Ranges:
// - local: 10010-10999
// - docker: 9050-9999
// - gateway: 7001-7999
const SERVICES: { [key: string]: ServiceConfig } = {
  api: { name: 'api', ports: { local: 10010, docker: 9051 } },
  apiWebhook: { name: 'api-webhook', ports: { local: 10011, docker: 9052 } },
  ui: { name: 'ui', ports: { local: 3013, docker: 9050 } },
  paymentsApi: { name: 'payments-api', ports: { local: 10012, docker: 9056 } },
  paymentsApiWebhook: { name: 'payments-api-webhook', ports: { local: 10013, docker: 9057 } },
  config: { name: 'config-server', ports: { local: 10056, docker: 9053 } },
  notification: { name: 'notification-server', ports: { local: 10057, docker: 9058 } },
  scheduler: { name: 'scheduler-server', ports: { local: 10016, docker: 9054, gateway: 7001 } },
  verification: {
    name: 'verification-server',
    ports: { local: 10052, docker: 9055, gateway: 7002 },
  },
  linking: {
    name: 'linking-server',
    ports: { local: 10058, docker: 9059, gateway: 7003 },
  },
  identity: { name: 'identity-server', ports: { local: 10059, docker: 9060, gateway: 7004 } },
};

export function nextPort(type: 'local' | 'docker' | 'gateway'): number {
  const ports = Object.values(SERVICES).map(({ ports }) => ports[type] ?? 0);
  return Math.max(...ports) + 1;
}

export default SERVICES;

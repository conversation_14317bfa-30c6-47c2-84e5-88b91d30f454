-- Revert drop-user_tokens-fk

BEGIN;
SET lock_timeout TO '5s';
SET search_path TO public;
SET ROLE migration;

-- Re-create the FK only when both the referencing column and the referenced table are present.
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
         WHERE table_schema = 'public' AND table_name = 'user_tokens' AND column_name = 'user_id'
    )
    AND EXISTS (
        SELECT 1 FROM information_schema.tables
         WHERE table_schema = 'public' AND table_name = 'users'
    )
    THEN
        EXECUTE 'ALTER TABLE public.user_tokens ADD CONSTRAINT user_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)';
    END IF;
END;
$$ LANGUAGE plpgsql;

COMMIT;

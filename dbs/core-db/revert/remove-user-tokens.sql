-- Revert remove-user-tokens

BEGIN;

SET search_path TO public;
SET ROLE migration;

-- Re-create the "user_tokens" table exactly as it existed prior to removal.
CREATE TABLE public.user_tokens (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone DEFAULT (now() + '24:00:00'::interval),
    token text NOT NULL,
    deactivated_at timestamp with time zone
);

-- Indexes
CREATE INDEX user_tokens_user_id_idx ON public.user_tokens USING btree (user_id);

-- Foreign-key constraints
ALTER TABLE ONLY public.user_tokens
    ADD CONSTRAINT user_tokens_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.user_tokens
    ADD CONSTRAINT user_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);

COMMIT;

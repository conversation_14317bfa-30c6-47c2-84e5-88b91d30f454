-- Verify drop-user_tokens-fk

BEGIN;
SET search_path TO public;
SET ROLE migration;

-- Fail if the FK constraint still exists on public.user_tokens.
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
         WHERE table_schema = 'public'
           AND table_name = 'user_tokens'
           AND constraint_name = 'user_tokens_user_id_fkey'
           AND constraint_type = 'FOREIGN KEY'
    ) THEN
        RAISE EXCEPTION 'Constraint user_tokens_user_id_fkey still exists on public.user_tokens';
    END IF;
END;
$$ LANGUAGE plpgsql;

ROLLBACK;

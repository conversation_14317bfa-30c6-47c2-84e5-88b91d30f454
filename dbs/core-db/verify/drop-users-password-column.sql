-- Verify: drop-users-password-column

BEGIN;
  -- Ensure we are operating in the proper schema / role
  SET search_path TO public;
  SET ROLE migration;

  -- Verify that the password column has been removed from the users table.
  -- If the column is still present, raise an exception so Sqitch verify fails.
  DO $$
  BEGIN
    IF EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_schema = 'public'
        AND table_name   = 'users'
        AND column_name  = 'password'
    ) THEN
      RAISE EXCEPTION 'Verification failed: column "password" still exists on table "users".';
    END IF;
  END;
  $$;

COMMIT;
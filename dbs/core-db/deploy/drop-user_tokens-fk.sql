-- Deploy drop-user_tokens-fk

BEGIN;
SET lock_timeout TO '5s';
SET search_path TO public;
SET ROLE migration;

-- fail fast if a long-running txn is in the way
SET lock_timeout = '5s'; 
-- Drop the FK on public.user_tokens(user_id) that references public.users(id) if it exists.
-- Earlier schemas never had this constraint, so IF EXISTS keeps the migration idempotent.
ALTER TABLE public.user_tokens
    DROP CONSTRAINT IF EXISTS user_tokens_user_id_fkey;

COMMIT;

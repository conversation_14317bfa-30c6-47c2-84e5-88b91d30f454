# Email-migration Cloud Run Job – Runbook (PLT-4269)

This guide shows how to run the Scholarship America applicant-email migration
script (`scripts/changeApplicantEmails.ts`) in GCP using **Cloud Run Jobs**.

It follows the procedure agreed in Slack (<#prod-ops 🔒>) and documented in
Linear ticket **PLT-4269**.

---

## 0.  Prerequisites

1. `gcloud` CLI ≥ 450.0  
   `gcloud components update`
2. Docker access to the target GCP project.
3. A service-account with:
   * `roles/run.admin`
   * `roles/secretmanager.secretAccessor`
   * `roles/storage.objectViewer` (if the CSV lives in GCS)
4. The following **Secret Manager** secrets (latest version will be pulled):
   * `CORE_DATABASE_URL`
   * `IDENTITY_DATABASE_URL`
   * `GOOGLE_PROJECT_ID`
   * `GOOGLE_APPLICATION_CREDENTIALS` – **JSON** SA key with editor access on
     the GCIP project.
5. A CSV (`oldEmail,newEmail` headers) accessible to the container at runtime
   (either baked into the image or mounted from GCS).

---

## 1.  Build & push the image

```bash
export PROJECT_ID=<your-gcp-project>
export REGION=us-central1
export IMAGE=gcr.io/$PROJECT_ID/email-migration:plt-4269

# From repo root
# The Dockerfile now installs *all* workspace dependencies and pre-builds the
# “*-entities” packages, so no extra build commands are required locally.
docker build -t $IMAGE -f cloudrun/email-migration/Dockerfile .
docker push $IMAGE
```

---

## 2.  Create the Job (one-time)

```bash
gcloud run jobs create email-migration-plt-4269 \
  --region $REGION \
  --image $IMAGE \
  --service-account "email-migration@$PROJECT_ID.iam.gserviceaccount.com" \
  --max-retries 0 \
  --set-secrets \
      CORE_DATABASE_URL=projects/$PROJECT_ID/secrets/CORE_DATABASE_URL:latest,\
      IDENTITY_DATABASE_URL=projects/$PROJECT_ID/secrets/IDENTITY_DATABASE_URL:latest,\
      GOOGLE_PROJECT_ID=projects/$PROJECT_ID/secrets/GOOGLE_PROJECT_ID:latest,\
      GOOGLE_APPLICATION_CREDENTIALS=projects/$PROJECT_ID/secrets/GOOGLE_APPLICATION_CREDENTIALS:latest
```

*The job is now registered but not executed.*

---

## 3.  Dry-run in **staging**

```bash
gcloud run jobs execute email-migration-plt-4269 \
  --region $REGION \
  --args="/workspace/emails-staging.csv"
```

Inspect logs in Cloud Run → Jobs → **Executions**.  
A JSON report (`email-migration-report.json`) is written to the container’s
working directory and appears in the logs.

---

## 4.  Production execution

```bash
gcloud run jobs execute email-migration-plt-4269 \
  --region $REGION \
  --args="/workspace/emails-prod.csv"
```

The script:

1. Updates Core **and** Identity DB records.
2. Resets `validatedEmail` / `verifiedEmail` flags **false** so the normal
   verification e-mail is issued.
3. Syncs the GCIP user (marked as `emailVerified=false`).
4. Skips duplicates and logs a JSON report.

* Flags `validatedEmail` / `verifiedEmail` are set to **false** – the new e-mail
  address will receive a standard Firebase verification message.

---

## 5.  Monitoring & Logs

```bash
gcloud logs read \
  "resource.type=cloud_run_job AND resource.labels.job_name=email-migration-plt-4269" \
  --project $PROJECT_ID
```

Each execution streams stdout/stderr; the final line prints the report path.

---

## 6.  Clean-up

```bash
# Delete executions (optional)
gcloud run jobs executions list --region $REGION \
  --job email-migration-plt-4269 --format="value(name)" | \
  xargs -I{} gcloud run jobs executions delete {} --region $REGION --quiet

# Delete the job
gcloud run jobs delete email-migration-plt-4269 --region $REGION --quiet
```

---

## 7.  Environment variables reference

| Variable | Secret name | Notes |
|----------|-------------|-------|
| `CORE_DATABASE_URL` | `CORE_DATABASE_URL` | Postgres → Core |
| `IDENTITY_DATABASE_URL` | `IDENTITY_DATABASE_URL` | Postgres → Identity |
| `GOOGLE_PROJECT_ID` | `GOOGLE_PROJECT_ID` | GCIP / Firebase tenant resolver |
| `GOOGLE_APPLICATION_CREDENTIALS` | `GOOGLE_APPLICATION_CREDENTIALS` | SA JSON file |

*(Optional)* Set `ENVIRONMENT=production` to force SSL in TypeORM.

---

## 8.  References

* Linear: **PLT-4269** – *“Bulk update Scholarship America applicant emails”*
* Slack: See `#prod-ops` thread dated 2025-06-30.

Happy migrating 🎉

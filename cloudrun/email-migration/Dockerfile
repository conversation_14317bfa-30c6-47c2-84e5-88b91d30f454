# ---- Email-migration one-off -------------------------------------------------
# This image is purpose-built for scripts/changeApplicantEmails.ts (PLT-4269)
# It installs workspace production deps and runs the script through ts-node.
# -----------------------------------------------------------------------------

FROM node:20.10.0-alpine AS base

ENV PNPM_HOME=/root/.local/share/pnpm
ENV PATH=$PNPM_HOME:$PATH

# 1. Enable pnpm via corepack (locked to repo version)
RUN apk add --no-cache \
  python3 \
  py3-pip \
  make \
  g++ \
  && corepack enable \
  && corepack prepare pnpm@10.4.1 --activate

# 1. Enable pnpm via corepack (locked to repo version)
RUN corepack enable && corepack prepare pnpm@10.4.1 --activate

WORKDIR /workspace

# 2. <PERSON><PERSON> the full repo so workspace packages are available for pnpm linking
COPY . .

# 3. Install *all* workspace dependencies (dev + prod).
RUN pnpm install --frozen-lockfile

# 4. Build entity packages required by ts-node at runtime.
#    Building ahead-of-time avoids TypeORM “entity not found” errors caused
#    by missing transpiled JS. We scope the build to packages ending in
#    “-entities” to keep the image small and the build fast.
RUN pnpm turbo run build

# 5. Global runtime tools – ts-node + typescript
RUN pnpm add -g ts-node typescript

ENV TS_NODE_COMPILER_OPTIONS={"moduleResolution":"NodeNext"}

# 6. Default entrypoint. Cloud Run will append the CSV path via --args.
ENTRYPOINT ["pnpm","ts-node","--transpile-only","scripts/changeApplicantEmails.ts"]

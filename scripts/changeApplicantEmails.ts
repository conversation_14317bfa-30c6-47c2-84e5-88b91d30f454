#!/usr/bin/env -S ts-node --transpile-only
/**
 * Bulk update applicant email addresses (PLT-4269)
 *
 * Usage:
 *   pnpm ts-node scripts/changeApplicantEmails.ts ./emails.csv
 *
 * The CSV must contain the headers `oldEmail` and `newEmail`.
 *
 * Behaviour:
 *   - For every row it updates *both* the Core and Identity databases so the
 *     applicant keeps a single account.
 *   - Flags `validatedEmail` (core) and `verifiedEmail` (identity) are reset to
 *     **false** so that the next login (or Firebase-Auth policies) sends the
 *     standard verification e-mail to the user.
 *   - The corresponding GCIP (Firebase) user is updated so SSO stays in sync.
 *   - If the `newEmail` already exists for the same partner the row is skipped
 *     and reported for manual follow-up.
 *
 * Environment variables expected:
 *   CORE_DATABASE_URL      – Postgres URL for the Core DB
 *   IDENTITY_DATABASE_URL  – Postgres URL for the Identity DB
 *   GOOGLE_PROJECT_ID      – Firebase project (used to pick the correct tenant)
 *   GOOGLE_APPLICATION_CREDENTIALS – path to a service-account JSON with the
 *                                    editor role on the GCIP project
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { exit } from 'node:process';
import * as IdentityEntities from '@bybeam/identity-entities';
import * as CoreEntities from '@bybeam/platform-entities';
import csv from 'csvtojson';
import admin from 'firebase-admin';
import { DataSource } from 'typeorm';

interface Row {
  oldEmail: string;
  newEmail: string;
}

interface ReportItem {
  oldEmail: string;
  newEmail?: string;
  status: string;
  detail?: string;
}

async function bootstrapDataSources() {
  const local = !process.env.ENVIRONMENT || process.env.ENVIRONMENT === 'local';
  const ssl = local ? false : { rejectUnauthorized: false };

  const core = new DataSource({
    type: 'postgres',
    url: process.env.CORE_DATABASE_URL,
    entities: Object.values(CoreEntities),
    synchronize: false,
    installExtensions: false,
    ssl,
  });

  const identity = new DataSource({
    type: 'postgres',
    url: process.env.IDENTITY_DATABASE_URL,
    entities: Object.values(IdentityEntities),
    synchronize: false,
    installExtensions: false,
    ssl,
  });

  await core.initialize();
  await identity.initialize();

  return { core, identity };
}

async function main() {
  const csvPath = process.argv[2];
  if (!csvPath) {
    console.error('CSV file is required: pnpm ts-node scripts/changeApplicantEmails.ts <file>');
    exit(1);
  }

  const absPath = path.resolve(csvPath);
  if (!(await fs.stat(absPath).catch(() => false))) {
    console.error(`CSV not found: ${absPath}`);
    exit(1);
  }

  const rows: Row[] = await csv().fromFile(absPath);
  if (!rows.length) {
    console.error('No rows found in CSV');
    exit(1);
  }

  const { core, identity } = await bootstrapDataSources();
  const coreUserRepo = core.getRepository(CoreEntities.UserEntity);
  const applicantRepo = identity.getRepository(IdentityEntities.ApplicantEntity);
  const identityUserRepo = identity.getRepository(IdentityEntities.UserEntity);
  const gcipRepo = identity.getRepository(IdentityEntities.GCIPUserEntity);

  // Firebase init (applicationDefault picks up GOOGLE_APPLICATION_CREDENTIALS)
  const firebaseApp = admin.initializeApp(
    {
      credential: admin.credential.applicationDefault(),
      projectId: process.env.GOOGLE_PROJECT_ID,
    },
    'email-migration',
  );

  const report: ReportItem[] = [];

  for (const r of rows) {
    const oldEmail = r.oldEmail.toLowerCase().trim();
    const newEmail = r.newEmail.toLowerCase().trim();

    try {
      // 1. Fetch the applicant in Core DB
      const coreUser = await coreUserRepo.findOne({ where: { email: oldEmail } });
      if (!coreUser) {
        report.push({ oldEmail, status: 'skipped', detail: 'old email not found in core' });
        continue;
      }

      const partnerId = coreUser.partnerId;

      // 2. Duplicate check in Core DB (same partner)
      const duplicate = await coreUserRepo.exist({ where: { email: newEmail, partnerId } });
      if (duplicate) {
        report.push({ oldEmail, newEmail, status: 'skipped', detail: 'new email already in use' });
        continue;
      }

      // 3. Identity linkage (Applicant row shares ID with Core user)
      const applicant = await applicantRepo.findOne({ where: { id: coreUser.id } });
      if (!applicant) {
        report.push({
          oldEmail,
          newEmail,
          status: 'skipped',
          detail: 'no applicant link in identity DB',
        });
        continue;
      }
      const identityUserId = applicant.userId;
      const identityUser = await identityUserRepo.findOne({ where: { id: identityUserId } });
      if (!identityUser) {
        report.push({ oldEmail, newEmail, status: 'skipped', detail: 'identity user missing' });
        continue;
      }

      // 4. Transaction per user – core & identity DBs
      await core.transaction(async (coreTx) => {
        await coreTx.update(
          CoreEntities.UserEntity,
          { id: coreUser.id },
          {
            email: newEmail,
            // force re-verification after the e-mail change
            validatedEmail: false,
          },
        );
      });

      await identity.transaction(async (idTx) => {
        await idTx.update(
          IdentityEntities.UserEntity,
          { id: identityUserId },
          {
            email: newEmail,
            verifiedEmail: false,
          },
        );
      });

      // 5. Update GCIP (Firebase)
      const gcip = await gcipRepo.findOne({ where: { userId: identityUserId } });
      if (gcip) {
        const tenantAuth = admin.auth(firebaseApp).tenantManager().authForTenant(gcip.tenantId);
        // Mark as *un-verified* so Firebase will require / trigger verification
        await tenantAuth.updateUser(gcip.uid, { email: newEmail, emailVerified: false });
      }

      report.push({ oldEmail, newEmail, status: 'updated' });
    } catch (err) {
      report.push({
        oldEmail: r.oldEmail,
        newEmail: r.newEmail,
        status: 'error',
        detail: (err as Error).message,
      });
    }
  }

  const out = path.resolve('email-migration-report.json');
  await fs.writeFile(out, JSON.stringify(report, null, 2));
  console.log(`Migration completed. Report written to ${out}`);

  await Promise.all([core.destroy(), identity.destroy()]);
}

main().catch((err) => {
  console.error(err);
  exit(1);
});

import {
  Address,
  Admin,
  AggregatePayments,
  ApplicantProfile,
  Application,
  BankAccount,
  CommunicationPreferences,
  Document,
  Enrollment,
  Partner,
  Payment,
  ProgramReferral,
  SavedView,
  TaxForm,
  User,
} from '@bybeam/platform-types';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EncryptionTransformer } from 'typeorm-encrypted';
import AdminEntity from './AdminEntity.js';
import { ApplicantProfileEntity } from './ApplicantProfileEntity.js';
import ApplicationEntity from './ApplicationEntity.js';
import DocumentEntity from './DocumentEntity.js';
import EnrollmentEntity from './EnrollmentEntity.js';
import PartnerEntity from './PartnerEntity.js';
import PaymentEntity from './PaymentEntity.js';
import ProgramReferralEntity from './ProgramReferralEntity.js';
import SavedViewEntity from './SavedViewEntity.js';
import TaxFormEntity from './TaxFormEntity.js';
import { displayId } from './utils/transformers.js';

@Entity('users')
export class UserEntity implements User {
  @PrimaryGeneratedColumn('uuid') public id: string;

  @Column({
    type: 'bigint',
    name: 'display_id',
    transformer: displayId('U'),
  })
  public displayId: string;

  @Column({ type: 'varchar', length: 255, name: 'legacy_id', nullable: true })
  public legacyId?: string;

  @Column('uuid', { name: 'partner_id' })
  public partnerId: string;

  @ManyToOne(() => PartnerEntity)
  @JoinColumn({ name: 'partner_id' })
  public partner: Partner;

  @Column({ type: 'varchar', length: 255 })
  public email: string;

  @Column({ type: 'varchar', length: 25 })
  public phone?: string;

  @Column({ type: 'varchar', length: 255 })
  public name: string;

  @Column({ type: 'jsonb', name: 'communication_preferences', nullable: true })
  public communicationPreferences?: CommunicationPreferences;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  public createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  public updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deactivated_at' })
  public deactivatedAt?: Date;

  @Column({ type: 'boolean', name: 'validated_email' })
  public validatedEmail: boolean;

  @Column({
    type: 'varchar',
    name: 'tax_id',
    transformer: new EncryptionTransformer({
      key: process.env.ENCRYPTION_KEY,
      algorithm: 'aes-256-cbc',
      ivLength: 16,
    }),
  })
  public taxId?: string;

  @OneToOne(
    () => AdminEntity,
    (admin) => admin.user,
  )
  public admin?: Admin;

  @OneToMany(
    () => EnrollmentEntity,
    (enrollment) => enrollment.applicant,
  )
  public enrollments?: Enrollment[];

  @OneToMany(
    () => ApplicationEntity,
    (application) => application.submitter,
  )
  public applications?: Application[];

  @OneToOne(
    () => ApplicantProfileEntity,
    (applicantProfile) => applicantProfile.user,
  )
  public applicantProfile?: ApplicantProfile;

  @OneToMany(
    () => PaymentEntity,
    (payment) => payment.payeeId,
  )
  public payment?: Payment[];

  @OneToMany(
    () => TaxFormEntity,
    (taxForm) => taxForm.user,
  )
  public taxForms?: TaxForm[];

  @OneToMany(
    () => ProgramReferralEntity,
    (referral) => referral.user,
  )
  public referrals?: ProgramReferral[];

  @ManyToMany(() => DocumentEntity)
  @JoinTable({
    name: 'user_documents',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'document_id', referencedColumnName: 'id' },
  })
  public documents: Document[];

  @OneToMany(
    () => SavedViewEntity,
    (savedView) => savedView.author,
  )
  public savedViews?: SavedView[];

  // For consistency with type
  public aggregatePayments?: AggregatePayments;
  public bankAccount?: BankAccount;
  public mailingAddress?: Address;
  public newEmail?: string;
}

export default UserEntity;

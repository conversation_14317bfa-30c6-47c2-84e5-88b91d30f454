import { Document, User } from '@bybeam/platform-types';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import UserEntity from './UserEntity.js';

@Entity('documents', {
  orderBy: {
    createdAt: 'DESC',
  },
})
export class DocumentEntity implements Document {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'varchar', length: 255, name: 'document_key' })
  public documentKey: string;

  @Column({ type: 'varchar', length: 255 })
  public filename: string;

  @Column({ type: 'varchar', length: 100 })
  public mimetype: string;

  @Column({ type: 'uuid', name: 'uploader_id' })
  public uploaderId: string;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'uploader_id' })
  public uploader?: User;

  @Column({ type: 'boolean' })
  public pinned: boolean;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  public createdAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deactivated_at' })
  public deactivatedAt?: Date;

  @Column({ type: 'varchar', nullable: true })
  public sha256?: string;
}

export default DocumentEntity;

import { DocumentField, DocumentTag, Feedback, Summary } from '@bybeam/doctopus-types';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import Admin from './admin.js';
import { YesNoUnsure } from './answers/rental.js';
import User from './user.js';

export interface Document {
  id: string;
  documentKey: string;
  uploaderId: string;
  filename: string;
  mimetype: string;
  createdAt: Date;
  pinned?: boolean;
  previewUrl?: string;
  legacyId?: string;
  uploader?: User;
  deactivatedAt?: Date;
  summary?: Summary;
  documentTags?: DocumentTag[];
  documentFields?: DocumentField[];
  sha256?: string;
}

export const DocumentRelationTypes = [
  'application',
  'case',
  'profile',
  'program',
  'user',
  'vendor',
] as const;
export type DocumentRelationType = (typeof DocumentRelationTypes)[number];

export interface DocumentRelation {
  type: DocumentRelationType;
  id: string;
}
export interface UploadDocumentsInput {
  relation: DocumentRelation;
  files: Promise<FileUpload>[];
  programId?: string;
}

export interface UploadEntityDocumentsInput {
  id: string;
  files: Promise<FileUpload>[];
}

export interface UploadEntityDocumentInput {
  id: string;
  file: Promise<FileUpload>;
}

export interface RemoveEntityDocumentsInput {
  id: string;
  documentIds: string[];
}
export interface PinDocumentInput {
  id: string;
  pinned: boolean;
}

export interface SubmitPredictionFeedbackInput {
  id: string;
  accurate: YesNoUnsure;
  preferredLabelId?: string;
}

export interface PredictionFeedback extends Feedback {
  accurate: YesNoUnsure;
  admin: Admin;
}

export const VALID_FILE_TYPES = [
  'heic',
  'heif',
  'jpg',
  'jpeg',
  'png',
  'webp',
  'pdf',
  'doc',
  'docx',
  'csv',
  'xls',
  'xlsx',
];

export const ALLOWED_MIME_TYPES = [
  'image/png',
  'image/jpeg',
  'image/heic',
  'image/heif',
  'image/webp',
  'doc',
  'application/msword',
  'officedocument.wordprocessingml.document',
  'application/pdf',
  'application/csv',
  'text/csv',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

export const RANDOM_FEEDBACK_SOLICITATION_PROBABILITY = 0.1;
export const APPLICATION_DOCUMENT_TOOLTIP =
  'Application documents can only be removed by creating a new application version and manually removing existing documents.';

export const SYSTEM_GENERATED = 'System Generated';

export default Document;

import Address, { CreateAddressInput } from './address.js';
import Admin from './admin.js';
import ApplicantProfile from './applicantProfile.js';
import Application from './application.js';
import { CommunicationChannels } from './communication.js';
import Document from './document.js';
import { Enrollment } from './enrollment.js';
import Partner from './partner.js';
import { BankAccount } from './payee.js';
import { AggregatePayments } from './payment.js';
import ProgramReferral from './programReferral.js';
import SavedView from './savedView.js';
import TaxForm from './taxForm.js';

export type CommunicationPreferences = {
  [key in CommunicationChannels]: boolean;
};

export interface User {
  id: string;
  displayId: string;
  partnerId: string;
  email: string;
  phone?: string;
  name: string;
  createdAt: Date;
  updatedAt?: Date;
  deactivatedAt?: Date;
  validatedEmail?: boolean;
  communicationPreferences?: CommunicationPreferences;
  taxId?: string;
  admin?: Admin;
  partner?: Partner;
  enrollments?: Enrollment[];
  legacyId?: string;
  newEmail?: string;
  applications?: Application[];
  bankAccount?: BankAccount;
  mailingAddress?: Address;
  applicantProfile?: ApplicantProfile;
  aggregatePayments?: AggregatePayments;
  taxForms?: TaxForm[];
  referrals?: ProgramReferral[];
  documents?: Document[];
  savedViews?: SavedView[];
}

export enum LegacyUserType {
  UnlinkedLegacyUser = 'UnlinkedLegacyUser',
  LinkedLegacyUser = 'LinkedLegacyUser',
  NonLegacyUser = 'NonLegacyUser',
}
export interface UserFilter {
  id?: string;
  profileId?: string;
  legacyUserType?: LegacyUserType;
  search?: string;
}

export enum UserSortColumn {
  Name = 'Name',
  TotalPaid = 'TotalPaid',
}

export interface CreateUserInput {
  partnerId: string;
  email: string;
  name: string;
  phone?: string;
  applicantTypeId: string;
}

export interface UpdateUserInput {
  id: string;
  secondaryEmail?: string;
  email?: string;
  name: string;
  phone?: string;
  taxId?: string;
  bankAccount?: BankAccount;
  communicationPreferences?: CommunicationPreferences;
}

export interface UnsubscribeCommunicationChannelInput {
  filter: { id?: string; phone?: string; email?: string };
  channel: CommunicationChannels;
}

export interface SendSMSHelpInput {
  filter: { id?: string; phone?: string; email?: string };
}

export interface UpdateUserProfileInput extends Omit<UpdateUserInput, 'id'> {
  applicantTypeId?: string;
  mailingAddress?: CreateAddressInput;
}

export interface ChangePrimaryEmailInput {
  userId: string;
  newEmail: string;
}

export interface LinkToLegacyUserInput {
  id: string;
  legacyUserId: string;
}

export interface SaveUserBankAccountInput {
  id: string;
  bankAccount: BankAccount;
}

export default User;

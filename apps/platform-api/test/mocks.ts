import {
  Address,
  ApplicationVersion,
  FeatureName,
  IncomeLimitArea,
  ProgramFeature,
  ProgramStatus,
  W9FormData,
} from '@bybeam/platform-types';
import {
  AdminRepository,
  ApplicationRepository,
  ApplicationScoreRepository,
  ApplicationVersionRepository,
  FundRepository,
  PartnerRepository,
  UserRepository,
  VendorTypeRepository,
  WorkflowEventRepository,
} from '@platform-api/@types/repositories/index.js';
import { ApplicationAnswerService } from '@platform-api/@types/services.js';
import AdminService from '@platform-api/services/admins/AdminService.js';
import ApplicationScoreService from '@platform-api/services/applicationScoring/ApplicationScoreService.js';
import ApplicationVersionService from '@platform-api/services/applicationVersions/ApplicationVersionService.js';
import ApplicationService from '@platform-api/services/applications/ApplicationService.js';
import AssignmentService from '@platform-api/services/assignments/AssignmentService.js';
import FundService from '@platform-api/services/funds/FundService.js';
import PartnerService from '@platform-api/services/partners/PartnerService.js';
import ProgramFeatureService from '@platform-api/services/programFeatures/ProgramFeatureService.js';
import ProgramService from '@platform-api/services/programs/ProgramService.js';
import UserService from '@platform-api/services/users/UserService.js';
import VendorTypeService from '@platform-api/services/vendorTypes/VendorTypeService.js';
import WorkflowEventService from '@platform-api/services/workflowEvent/WorkflowEventService.js';

export const mockLoginToken = {
  identityUserId: 'mockIdentityUserId',
  gcipUid: 'mockGcipUID',
  userId: 'mockUserUUID',
  partnerId: 'mockPartnerId',
  tenantId: 'mock-applicant-tenant',
  sessionId: 'mockSessionId',
};
export const mockAdminToken = {
  ...mockLoginToken,
  adminId: 'mockAdminId',
  tenantId: 'mock-advocate-tenant',
};

const mockAdmin = {
  id: 'mockAdminId',
  userId: 'mockUserId',
  partnerId: 'mockPartnerId',
};

const mockUser = {
  id: 'mockUserUUID',
  partnerId: 'mockPartnerId',
  email: '<EMAIL>',
  phone: '5555555555',
  name: 'Mock User',
  password: '$2b$11$kyCa/0kVlwH2bhBknF8H7ebaJR8VQenZKKbGI.UEGhPVZZrSZSk1K',
  validatedEmail: true,
  taxId: 'mockedTaxId',
};

const mockProgram = {
  id: 'mockProgramUUID',
  partner: { id: 'mockPartnerId' },
  status: ProgramStatus.Open,
};

export const feature = (name: FeatureName, enabled = true): ProgramFeature =>
  ({ feature: { name }, enabled }) as ProgramFeature;

const mockProgramFeature = {
  id: 'mockProgramFeatureId',
  featureId: 'mockFeatureId',
  programId: mockProgram.id,
  enabled: false,
};

const mockApplication = { id: 'mockApplicationUUID' };

const mockIncomeLimit = {
  limits: {
    low: {
      il80_p1: 52950,
      il80_p2: 60500,
      il80_p3: 68050,
      il80_p4: 75600,
      il80_p5: 81650,
      il80_p6: 87700,
      il80_p7: 93750,
      il80_p8: 99800,
    },
    very_low: {
      il50_p1: 33100,
      il50_p2: 37800,
      il50_p3: 42550,
      il50_p4: 47250,
      il50_p5: 51050,
      il50_p6: 54850,
      il50_p7: 58600,
      il50_p8: 62400,
    },
    extremely_low: {
      il30_p1: 19850,
      il30_p2: 22700,
      il30_p3: 25550,
      il30_p4: 28350,
      il30_p5: 31040,
      il30_p6: 35580,
      il30_p7: 40120,
      il30_p8: 44660,
    },
  },
};

const mockCase = { id: 'mockCaseUUID' };

export const mockIncomeLimitArea = {
  id: 'mockIncomeLimitAreaUUID',
  county: 'Philadelphia',
  state: 'PA',
  fipsCode: '42101',
  incomeLimit: mockIncomeLimit,
} as Omit<IncomeLimitArea, 'incomeLimit'>;

export const mockIncomeLimitAreaResult = {
  ...mockIncomeLimitArea,
  incomeLimit: {
    limits: {
      low: [
        { householdSize: 1, incomeLimit: 52950 },
        { householdSize: 2, incomeLimit: 60500 },
        { householdSize: 3, incomeLimit: 68050 },
        { householdSize: 4, incomeLimit: 75600 },
        { householdSize: 5, incomeLimit: 81650 },
        { householdSize: 6, incomeLimit: 87700 },
        { householdSize: 7, incomeLimit: 93750 },
        { householdSize: 8, incomeLimit: 99800 },
      ],
      veryLow: [
        { householdSize: 1, incomeLimit: 33100 },
        { householdSize: 2, incomeLimit: 37800 },
        { householdSize: 3, incomeLimit: 42550 },
        { householdSize: 4, incomeLimit: 47250 },
        { householdSize: 5, incomeLimit: 51050 },
        { householdSize: 6, incomeLimit: 54850 },
        { householdSize: 7, incomeLimit: 58600 },
        { householdSize: 8, incomeLimit: 62400 },
      ],
      extremelyLow: [
        { householdSize: 1, incomeLimit: 19850 },
        { householdSize: 2, incomeLimit: 22700 },
        { householdSize: 3, incomeLimit: 25550 },
        { householdSize: 4, incomeLimit: 28350 },
        { householdSize: 5, incomeLimit: 31040 },
        { householdSize: 6, incomeLimit: 35580 },
        { householdSize: 7, incomeLimit: 40120 },
        { householdSize: 8, incomeLimit: 44660 },
      ],
    },
  },
} as IncomeLimitArea;

export const mockAddress = {
  id: 'mockAddressUUID',
  addressLine1: '1900 Locust Street',
  city: 'Philadelphia',
  state: 'PA',
  zip: '19129',
  incomeLimitArea: mockIncomeLimitArea,
} as Address;

const mockPartner = {
  id: 'mockPartnerId',
  externalId: 'partner',
  parentId: 'mockParentPartnerId',
  whitelabeling: {
    logo: 'mockLogo',
  },
  email: '<EMAIL>',
  phone: '5555555555',
  name: 'Mock Partner',
};

const mockVendorTypeIds = {
  id: 'mockVendorTypeUUID',
  vendorTypeIds: ['mockVendorTypeUUID'],
};

const mockVersion = {
  id: 'mockVersionId',
  applicationId: 'mockApplicationId',
  creatorId: 'mockUserId',
} as unknown as ApplicationVersion;

const mockApplicationScore = {
  id: 'b36d7af6-678c-48a4-af10-c84b42c35bcf',
  applicationVersionId: 'e080fc2d-5816-45dc-99c4-7fbb71729948',
  score: 2,
  algorithmVersion: 3,
};

export const version = () => {
  const repository = {
    find: vi.fn().mockReturnValue([mockVersion]),
    save: vi.fn().mockReturnValue(mockVersion),
    findLatestVersions: vi.fn().mockReturnValue([mockVersion]),
  } as unknown as ApplicationVersionRepository;
  return {
    record: mockVersion,
    repository,
    service: new ApplicationVersionService(repository),
  };
};

export const admin = () => {
  const repository = {
    find: vi.fn().mockResolvedValue([]),
    count: vi.fn().mockResolvedValue(0),
  } as unknown as AdminRepository;
  return {
    record: mockAdmin,
    repository,
    service: new AdminService(
      repository,
      user().service,
      workflowEvent().service,
      {} as unknown as AssignmentService,
    ),
  };
};

export const program = () => ({
  record: mockProgram,
  service: {
    findById: vi.fn().mockReturnValue(mockProgram),
  } as unknown as ProgramService,
});

export const programFeature = ({ enabled }: { enabled: boolean }) => ({
  record: mockProgramFeature,
  service: {
    hasFeature: vi.fn().mockResolvedValue(enabled),
    hasFeatures: vi.fn().mockResolvedValue(enabled),
    findByProgramId: vi.fn().mockResolvedValue({ ...mockProgramFeature, enabled }),
  } as unknown as ProgramFeatureService,
});

export const user = () => {
  const repository = {
    findById: vi.fn().mockReturnValue(mockUser),
    findByIds: vi.fn().mockReturnValue([mockUser]),
    findOne: vi.fn().mockReturnValue(mockUser),
    lookup: vi.fn().mockReturnValue(mockUser),
    save: vi.fn().mockReturnValue(mockUser),
  } as unknown as UserRepository;
  const service = new UserService(repository);
  return {
    record: mockUser,
    repository,
    service,
  };
};

export const partner = () => {
  const repository = {
    findById: vi.fn().mockReturnValue(mockPartner),
    findByIds: vi.fn().mockReturnValue([mockPartner]),
    findOne: vi.fn().mockReturnValue(mockPartner),
  } as unknown as PartnerRepository;
  const service = new PartnerService(repository);
  return { record: mockPartner, repository, service };
};

export const workflowEvent = () => {
  const repository = {
    save: vi.fn(),
  } as unknown as WorkflowEventRepository;
  return { repository, service: new WorkflowEventService(repository) };
};

export const fund = () => {
  const repository = {} as unknown as FundRepository;
  return {
    repository,
    service: new FundService(repository),
    record: { id: 'mockFundId', name: 'Mock Fund' },
  };
};

export const case_ = () => {
  return {
    record: mockCase,
  };
};

export const application = () => {
  const repository = {
    find: vi.fn().mockReturnValue([]),
    findOne: vi.fn().mockReturnValue(undefined),
    save: vi.fn().mockReturnValue(mockApplication),
  } as unknown as ApplicationRepository;
  const service = new ApplicationService(
    repository,
    {} as ApplicationAnswerService,
    version().service,
  );
  return {
    record: mockApplication,
    repository,
    service,
  };
};

export const vendorType = () => {
  const repository = {
    findByVendorIds: vi.fn().mockReturnValue([mockVendorTypeIds]),
  } as unknown as VendorTypeRepository;
  const service = new VendorTypeService(repository);
  return {
    repository,
    service,
  };
};

export const score = (score: number) => {
  const record = { ...mockApplicationScore, score };
  const repository = {
    save: vi.fn().mockReturnValue(record),
  } as unknown as ApplicationScoreRepository;
  const service = new ApplicationScoreService(repository, version().service);
  return {
    repository,
    service,
    record,
  };
};

export const taxForms = () => {
  const w9FormData = {
    tin: 'SocialSecurityNumber',
    ssn: '*********',
    taxClassification: 'Individual',
    name: 'Rafael Pirotto',
    addressLine1: 'Benito Blanco 1078',
    addressLine2: 'Apto 501',
    city: 'Montevideo',
    state: 'FL',
    zip: '123123',
    signature: 'Rafael Pirotto',
  } as W9FormData;
  return { record: w9FormData };
};

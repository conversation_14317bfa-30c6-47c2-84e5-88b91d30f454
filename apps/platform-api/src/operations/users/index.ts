import { EventProducer } from '@platform-api/@types/events.js';
import { Operations } from '@platform-api/@types/operations.js';
import { Services } from '@platform-api/@types/services.js';
import buildRepositories from '../../repositories/index.js';
import CreateApplicantOperation from './CreateApplicantOperation.js';
import CreateW9TaxFormOperation from './CreateW9TaxFormOperation.js';
import EditUserDetailsOperation from './EditUserDetailsOperation.js';
import LinkLegacyUserOperation from './LinkLegacyUserOperation.js';
import UnsubscribeCommunicationChannel from './UnsubscribeCommunicationChannel.js';
import UpdateUserProfileOperation from './UpdateUserProfileOperation.js';
import SendSMSHelp from './SendSMSHelp.js';
import CreateBulkProgramReferralOperation from './CreateBulkProgramReferral.js';

export const build = (
  {
    addresses,
    applicantProfiles,
    applications,
    applicationVersions,
    bulkOperations,
    cases,
    documents,
    externalNotifications,
    enrollments,
    identity,
    partners,
    payments,
    programs,
    programReferrals,
    users,
    workflowEvents,
    taxForms,
  }: Services,
  { upsertBankAccount }: Operations['payments'],
  events: EventProducer,
): Operations['users'] => {
  const editDetails = new EditUserDetailsOperation({
    applicantProfiles,
    applications,
    cases,
    users,
    externalNotifications,
    identity,
    partners,
    upsertBankAccount,
    workflowEvents,
  });
  const updateProfile = new UpdateUserProfileOperation({
    addresses,
    applicantProfiles,
    applications,
    cases,
    externalNotifications,
    identity,
    partners,
    users,
    workflowEvents,
  });
  const linkLegacyUser = new LinkLegacyUserOperation({
    applications,
    applicationVersions,
    cases,
    documents,
    enrollments,
    payments,
    users,
    workflowEvents,
    transactionAuditLog: buildRepositories().TransactionAuditLog,
  });
  const createApplicant = new CreateApplicantOperation({
    identity,
    users,
    workflowEvents,
  });
  const createW9TaxForm = new CreateW9TaxFormOperation({
    users,
    documents,
    taxForms,
  });
  const unsubscribeCommunicationChannel = new UnsubscribeCommunicationChannel({
    users,
    events,
    workflowEvents,
  });
  const sendSMSHelp = new SendSMSHelp({
    users,
    events,
  });

  const createBulkProgramReferral = new CreateBulkProgramReferralOperation({
    applications,
    bulkOperations,
    externalNotifications,
    programs,
    programReferrals,
    users,
    workflowEvents,
  });

  return {
    createApplicant,
    createW9TaxForm,
    createBulkProgramReferral,
    editDetails,
    linkLegacyUser,
    sendSMSHelp,
    unsubscribeCommunicationChannel,
    updateProfile,
  };
};

import { Relation } from '@bybeam/identity-client/types';
import {
  CreateUserInput,
  MutationResponse,
  Nullable,
  User,
  WorkflowAction,
} from '@bybeam/platform-types';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { Services } from '@platform-api/@types/services.js';
import { StatusCodes } from 'http-status-codes';
import * as response from '../../utils/response.js';
import AbstractOperation from '../AbstractOperation.js';

/**
 * Creates a new applicant account. Previously, we had logic in here to link a
 * legacy user if found. However, that feature is now defacto deprecated and no
 * active programs are using it, so this operation has been simplified to create
 * only accounts for the applicant experience without linking to any legacy user.
 *
 * We can explore adding linking back in after the completion of modern migration
 * work and the entity resolution service.
 */
export default class CreateApplicantOperation extends AbstractOperation<
  Pick<Services, 'identity' | 'users' | 'workflowEvents'>,
  CreateUserInput,
  MutationResponse<User>,
  Nullable<AdminToken>
> {
  private async validateRequest({
    partnerId,
    email,
    name,
  }: CreateUserInput & Partial<AdminToken>): Promise<Nullable<response.ResponseMetadata<User>>> {
    if (!name) return { statusCode: StatusCodes.BAD_REQUEST, errors: ['No name provided'] };
    if (!email) return;
    const lookup = await this.dependencies.users.findWithOptions({
      where: { partnerId, email },
      relations: ['admin'],
    });
    if (lookup && (!lookup.legacyId || lookup.admin)) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: [`${email} already exists for another user`],
      };
    }
  }

  public async run(token: AdminToken, input: CreateUserInput): Promise<MutationResponse<User>> {
    const { partnerId, email, applicantTypeId } = input;
    this.log('info', `creating new user on partner ${partnerId}`);
    if (token) this.log('info', `admin ${token.adminId} is creating user`);

    const validationErr = await this.validateRequest({ ...input, ...(token as AdminToken) });
    if (validationErr) {
      this.log('warn', 'input validation error >', validationErr);
      return response.error(validationErr);
    }

    try {
      const user = await this.dependencies.identity.createUser({
        partnerId: input.partnerId,
        roles: [Relation.Applicant],
        email: input?.email,
        name: input?.name,
        phone: input?.phone,
        applicantTypeId,
        _applicantTypeId: 'applicantTypeId',
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        _password: 'password',
      });

      const coreUserId = user.applicants.find((app) => app.partnerId === partnerId)?.id;
      if (!coreUserId) throw new Error('failed to create associated applicant');

      await this.dependencies.workflowEvents.create(token, {
        entityId: coreUserId,
        entityType: 'user',
        action: WorkflowAction.UserCreate,
        newValue: JSON.stringify({ email, name: input.name, phone: input?.phone }),
      });

      const createdUser = await this.dependencies.users.findById(coreUserId);

      this.log('info', `created new user ${coreUserId}`);
      return response.success({
        statusCode: StatusCodes.CREATED,
        recordId: createdUser.id,
        record: createdUser,
      });
    } catch (error) {
      this.log('error', 'unexpected error >', { error });
      return response.error();
    }
  }
}

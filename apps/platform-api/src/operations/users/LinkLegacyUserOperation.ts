import {
  MutationResponse,
  TransactionOperation,
  TransactionStatus,
  User,
  WorkflowAction,
} from '@bybeam/platform-types';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { LinkLegacyUserOperationInput } from '@platform-api/@types/operations.js';
import { TransactionAuditLogRepository } from '@platform-api/@types/repositories/index.js';
import { Services } from '@platform-api/@types/services.js';
import { StatusCodes } from 'http-status-codes';
import { intersection } from '../../utils/array.js';
import * as response from '../../utils/response.js';
import AbstractOperation from '../AbstractOperation.js';

/**
 * Links a user/applicant with a legacy user (i.e., one that was carried over from a data migration).
 * In order for a user to be considered a "legacy" user, it must have a legacyId, and must not have
 * been previously linked (previously detected via a password column which has now
 * been removed).
 *
 * Linking to a legacy user entails setting the legacyId on the new user and transferring ownership
 * over the legacy user's:
 * - applications & application version
 * - cases
 * - documents
 * - enrollments
 * - payments
 *
 * Once linking is complete, the legacy user is deactivated.
 *
 * TODO: This is a bit brittle because it lacks a rollback capability. We need to figure
 * out a scalable transaction management solution so large operations like this can
 * handle failures gracefully without putting the database into a broken state.
 */
export default class LinkLegacyUserOperation extends AbstractOperation<
  Pick<
    Services,
    | 'applications'
    | 'applicationVersions'
    | 'cases'
    | 'documents'
    | 'enrollments'
    | 'payments'
    | 'users'
    | 'workflowEvents'
  > & { transactionAuditLog: TransactionAuditLogRepository },
  LinkLegacyUserOperationInput,
  MutationResponse<User>,
  AdminToken | undefined
> {
  private validateUsers(
    user?: User,
    legacyUser?: User,
  ): { statusCode: StatusCodes; errors?: string[] } | undefined {
    if (!user || !legacyUser) return { statusCode: StatusCodes.NOT_FOUND };
    if (!legacyUser.legacyId)
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Cannot link to a non-legacy user.'],
      };
    if (user.legacyId)
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['This user has already been linked.'],
      };
    if (legacyUser.admin) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Cannot link to an admin.'],
      };
    }
    if (user.admin) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['This user is an admin.'],
      };
    }
  }

  public async run(
    token: AdminToken | undefined,
    { id, legacyUserId, includeDeactivatedLegacyUser }: LinkLegacyUserOperationInput,
  ): Promise<MutationResponse<User>> {
    this.log('info', `attempting to link user ${id} to legacy user ${legacyUserId}`);

    let updateProgress: Record<string, string | string[]> & { status?: TransactionStatus } = {
      userId: id,
      legacyUserId,
    };
    try {
      const [user, legacyUser] = await Promise.all([
        this.dependencies.users.findWithOptions({
          where: { id },
          relations: ['admin'],
        }),
        this.dependencies.users.findWithOptions({
          where: { id: legacyUserId },
          withDeleted: !!includeDeactivatedLegacyUser,
          relations: ['admin'],
        }),
      ]);
      this.dependencies.users.resetCache(id);
      const userError = this.validateUsers(user, legacyUser);
      if (userError) return response.error(userError);

      if (token)
        await this.dependencies.workflowEvents.create(token, {
          entityId: id,
          entityType: 'user',
          action: WorkflowAction.LinkLegacyUser,
          previousValue: legacyUserId,
          newValue: id,
        });

      await this.dependencies.users.update({ id, legacyId: legacyUser?.legacyId });
      updateProgress = { ...updateProgress, legacyId: legacyUser?.legacyId ?? '' };

      // Update ownership of all of the legacy user's relations
      const caseIds = (await this.dependencies.applications.findBySubmitterId(legacyUserId)).map(
        ({ caseId }) => caseId,
      );
      this.dependencies.applications.resetCache(id);
      if (caseIds.length > 0) await this.dependencies.cases.update(caseIds, { name: user?.name });
      updateProgress = { ...updateProgress, cases: caseIds };

      updateProgress = {
        ...updateProgress,
        applications: await this.dependencies.applications.relinkUser({ id, legacyUserId }),
      };
      updateProgress = {
        ...updateProgress,
        applicationVersions: await this.dependencies.applicationVersions.relinkUser({
          id,
          legacyUserId,
        }),
      };
      updateProgress = {
        ...updateProgress,
        documents: await this.dependencies.documents.relinkUser({ id, legacyUserId }),
      };

      // If the legacy user has enrollments for the same program as the new user, deactivate
      // the new user's enrollments and transfer the legacy user's
      const [userEnrolledPrograms, legacyUserEnrolledPrograms] = (
        await Promise.all(
          [id, legacyUserId].map((userId) =>
            this.dependencies.enrollments.findByApplicantId(userId),
          ),
        )
      ).map((enrollments) => enrollments.map(({ programId }) => programId));
      this.dependencies.enrollments.resetCache(id);
      const commonProgramIds = intersection(userEnrolledPrograms, legacyUserEnrolledPrograms);
      if (commonProgramIds.length > 0)
        await this.dependencies.enrollments.deactivate(
          commonProgramIds.map((programId) => ({ programId, applicantId: id })),
        );
      updateProgress = {
        ...updateProgress,
        enrollments: await this.dependencies.enrollments.relinkUser({ id, legacyUserId }),
      };

      updateProgress = {
        ...updateProgress,
        payments: await this.dependencies.payments.relinkUser({ id, legacyUserId }),
      };

      await this.dependencies.users.deactivate(legacyUserId);

      updateProgress = { ...updateProgress, status: TransactionStatus.Success };
      return response.success({
        recordId: id,
        record: await this.dependencies.users.findById(id),
      });
    } catch (error) {
      this.log('error', 'unexpected error >', { error });

      // TODO: Figure out some way to have all of this run in a transaction so we can rollback,
      // or just do a manual rollback since we should have all of the affected ids here
      this.log('info', 'failed partially through linking legacy user >', updateProgress);
      updateProgress = { ...updateProgress, status: TransactionStatus.Failure };
      return response.error();
    } finally {
      const { status, ...metadata } = updateProgress;
      if (status)
        await this.dependencies.transactionAuditLog.save({
          operation: TransactionOperation.LinkLegacyUser,
          status,
          input: { id, legacyUserId },
          metadata,
        });
    }
  }
}

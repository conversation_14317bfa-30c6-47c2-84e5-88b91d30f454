import {
  ApplicantType<PERSON><PERSON>,
  MutationResponse,
  PayeeType,
  UpdateUserInput,
  User,
  WorkflowAction,
} from '@bybeam/platform-types';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { UpsertBankAccountOperation } from '@platform-api/@types/operations.js';
import { Services } from '@platform-api/@types/services.js';
import { StatusCodes } from 'http-status-codes';
import * as response from '../../utils/response.js';
import { pick } from '../../utils/set.js';
import AbstractOperation from '../AbstractOperation.js';

export default class EditUserDetailsOperation extends AbstractOperation<
  Pick<
    Services,
    | 'applicantProfiles'
    | 'applications'
    | 'cases'
    | 'externalNotifications'
    | 'identity'
    | 'partners'
    | 'users'
    | 'workflowEvents'
  > & {
    upsertBankAccount: UpsertBankAccountOperation;
  },
  UpdateUserInput,
  MutationResponse<User>,
  AdminToken
> {
  public async run(
    token: AdminToken,
    {
      id,
      name,
      phone,
      secondaryEmail,
      email: unsanitizedEmail,
      taxId,
      bankAccount,
    }: UpdateUserInput,
  ): Promise<MutationResponse<User>> {
    this.log('info', `attempting to update user details for ${id}`);

    try {
      const user = await this.dependencies.users.findById(id);
      if (!user) throw new Error('No user found');

      if (secondaryEmail) {
        const existingSecondaryEmailUser = await this.dependencies.users.findWithOptions({
          where: { email: secondaryEmail },
        });
        if (existingSecondaryEmailUser)
          this.log('warn', `Existing user for email ${secondaryEmail} with id ${id}`);
      }

      const email = unsanitizedEmail?.toLowerCase();
      const { partnerId } = user;

      if (bankAccount) {
        const payeeResp = await this.dependencies.upsertBankAccount.run(token, {
          payee: { payeeType: PayeeType.User, id },
          update: { email, name, bankAccount },
        });
        if (payeeResp.metadata.status !== StatusCodes.OK)
          return { metadata: payeeResp.metadata, query: {} };
      }

      const updatedUser = await this.dependencies.users.update({
        id,
        taxId,
      });

      await this.dependencies.identity.updateUser({
        partnerId,
        userIdentifier: 'coreUserId',
        coreUserId: id,
        email,
        name,
        phone,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        // deprecated: should be removed later
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });

      await this.dependencies.applicantProfiles.update([
        {
          userId: id,
          secondaryEmail,
        },
      ]);

      if (user.name !== name) {
        const applicantProfile = await this.dependencies.applicantProfiles.findWithOptions({
          where: { userId: user.id },
          relations: ['applicantType'],
        });

        if (applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty) {
          const caseIds = (await this.dependencies.applications.findBySubmitterId(id)).map(
            ({ caseId }) => caseId,
          );
          if (caseIds.length > 0) await this.dependencies.cases.update(caseIds, { name });
        }
      }

      this.dependencies.users.resetCache(id);

      await this.dependencies.workflowEvents.create(token, {
        entityId: user.id,
        entityType: 'user',
        action: WorkflowAction.UserUpdate,
        previousValue: JSON.stringify(pick(user, ['email', 'name', 'phone'])),
        newValue: JSON.stringify({ name, email, phone }),
        details:
          (!!user.taxId || !!updatedUser.taxId) && user.taxId !== updatedUser.taxId
            ? 'TaxId has been updated.'
            : '',
      });

      return response.success({
        recordId: id,
        record: { ...(await this.dependencies.users.findById(id)), bankAccount },
      });
    } catch (error) {
      this.log('error', 'unexpected error >', { error });
      return response.error();
    }
  }
}

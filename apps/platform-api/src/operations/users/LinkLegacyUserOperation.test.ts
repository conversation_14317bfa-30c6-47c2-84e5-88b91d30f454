import { TransactionOperation, TransactionStatus, WorkflowAction } from '@bybeam/platform-types';
import { mockAdminToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import { TransactionAuditLogRepository } from '@platform-api/@types/repositories/index.js';
import {
  ApplicationService,
  ApplicationVersionService,
  CaseService,
  DocumentService,
  EnrollmentService,
  PaymentService,
  UserService,
  WorkflowEventService,
} from '@platform-api/@types/services.js';
import LinkLegacyUserOperation from './LinkLegacyUserOperation.js';

const validLegacyUser = { id: 'legacyUser', legacyId: 'mockLegacyId', password: undefined };

describe('LinkLegacyUserOperation', () => {
  describe('input errors', () => {
    const mockUserFind = vi.fn();
    const operation = new LinkLegacyUserOperation({
      applications: {} as unknown as ApplicationService,
      applicationVersions: {} as unknown as ApplicationVersionService,
      cases: {} as unknown as CaseService,
      documents: {} as unknown as DocumentService,
      enrollments: {} as unknown as EnrollmentService,
      payments: {} as unknown as PaymentService,
      transactionAuditLog: {} as unknown as TransactionAuditLogRepository,
      users: {
        findWithOptions: mockUserFind,
        resetCache: vi.fn(),
      } as unknown as UserService,
      workflowEvents: {} as unknown as WorkflowEventService,
    });

    describe('when specified legacy user does not have a legacy id', () => {
      it('returns a 400 Bad Request and appropriate error', async () => {
        mockUserFind
          .mockResolvedValueOnce({ id: 'newUser' })
          .mockResolvedValueOnce({ id: 'legacyUser' });

        const resp = await operation.run(mockAdminToken, {
          id: 'newUser',
          legacyUserId: 'legacyUser',
        });

        expect(resp).toEqual({
          metadata: {
            status: 400,
            message: 'Bad Request',
            errors: ['Cannot link to a non-legacy user.'],
          },
          query: {},
        });
      });
    });

    describe('when the specified user has a legacy id (has already been linked)', () => {
      it('returns a 400 Bad Request and appropriate error', async () => {
        mockUserFind
          .mockResolvedValueOnce({ id: 'newUser', legacyId: 'linkedLegacyId' })
          .mockResolvedValueOnce(validLegacyUser);

        const resp = await operation.run(mockAdminToken, {
          id: 'newUser',
          legacyUserId: 'legacyUser',
        });

        expect(resp).toEqual({
          metadata: {
            status: 400,
            message: 'Bad Request',
            errors: ['This user has already been linked.'],
          },
          query: {},
        });
      });
    });

    describe('when the user has admin role', () => {
      it('returns a 400 Bad Request and appropriate error', async () => {
        mockUserFind
          .mockResolvedValueOnce({ id: 'newUser', admin: { id: 'adminNewUser' } })
          .mockResolvedValueOnce(validLegacyUser);

        const resp = await operation.run(mockAdminToken, {
          id: 'newUser',
          legacyUserId: 'legacyUser',
        });

        expect(resp).toEqual({
          metadata: {
            status: 400,
            message: 'Bad Request',
            errors: ['This user is an admin.'],
          },
          query: {},
        });
      });
    });
    describe('when legacy user has admin role', () => {
      it('returns a 400 Bad Request and appropriate error', async () => {
        mockUserFind
          .mockResolvedValueOnce({ id: 'newUser' })
          .mockResolvedValueOnce({ ...validLegacyUser, admin: { id: 'adminLegacyUser' } });

        const resp = await operation.run(mockAdminToken, {
          id: 'newUser',
          legacyUserId: 'legacyUser',
        });

        expect(resp).toEqual({
          metadata: {
            status: 400,
            message: 'Bad Request',
            errors: ['Cannot link to an admin.'],
          },
          query: {},
        });
      });
    });
  });

  describe('when the operation fails midway', () => {
    it('logs the progress made and creates a workflow event', async () => {
      const mockCreateWflwEvtFn = vi.fn();
      const mockSaveAuditLogFn = vi.fn();
      const operation = new LinkLegacyUserOperation({
        applications: {
          findBySubmitterId: vi.fn().mockResolvedValueOnce([{ caseId: 'case1' }]),
          relinkUser: vi.fn().mockResolvedValueOnce(['app1', 'app2']),
          resetCache: vi.fn(),
        } as unknown as ApplicationService,
        applicationVersions: {
          relinkUser: vi.fn().mockRejectedValueOnce(new Error('Uh oh! This is an error')),
        } as unknown as ApplicationVersionService,
        cases: { update: vi.fn() } as unknown as CaseService,
        documents: {} as unknown as DocumentService,
        enrollments: {} as unknown as EnrollmentService,
        payments: {} as unknown as PaymentService,
        transactionAuditLog: {
          save: mockSaveAuditLogFn,
        } as unknown as TransactionAuditLogRepository,
        users: {
          findWithOptions: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser' })
            .mockResolvedValueOnce(validLegacyUser),
          resetCache: vi.fn(),
          update: vi.fn(),
        } as unknown as UserService,
        workflowEvents: { create: mockCreateWflwEvtFn } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        legacyUserId: validLegacyUser.id,
      });

      expect(resp).toEqual({
        metadata: {
          status: 500,
          message: 'Internal Server Error',
          errors: ['Internal Server Error'],
        },
        query: {},
      });

      const expectedProgress = {
        userId: 'mockUser',
        legacyUserId: validLegacyUser.id,
        legacyId: validLegacyUser.legacyId,
        cases: ['case1'],
        applications: ['app1', 'app2'],
      };
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: new Error('Uh oh! This is an error') },
        'LinkLegacyUserOperation: unexpected error >',
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        expectedProgress,
        'LinkLegacyUserOperation: failed partially through linking legacy user >',
      );
      expect(mockCreateWflwEvtFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockUser',
        entityType: 'user',
        action: WorkflowAction.LinkLegacyUser,
        previousValue: validLegacyUser.id,
        newValue: 'mockUser',
      });
      expect(mockSaveAuditLogFn).toHaveBeenCalledWith({
        operation: TransactionOperation.LinkLegacyUser,
        status: TransactionStatus.Failure,
        input: { id: 'mockUser', legacyUserId: validLegacyUser.id },
        metadata: expectedProgress,
      });
    });
  });

  describe('when the operation succeeds', () => {
    it('returns a 200 and creates a workflow event with the change log', async () => {
      const mockCreateWflwEvtFn = vi.fn();
      const mockSaveAuditLogFn = vi.fn();
      const mockUpdateUserFn = vi.fn();
      const mockCaseUpdate = vi.fn();
      const mockDeactivateUserFn = vi.fn();
      const operation = new LinkLegacyUserOperation({
        applications: {
          findBySubmitterId: vi.fn().mockResolvedValueOnce([{ caseId: 'case1' }]),
          relinkUser: vi.fn().mockResolvedValueOnce(['app1', 'app2']),
          resetCache: vi.fn(),
        } as unknown as ApplicationService,
        applicationVersions: {
          relinkUser: vi.fn().mockResolvedValueOnce(['vers1', 'vers2', 'vers3']),
        } as unknown as ApplicationVersionService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        documents: {
          relinkUser: vi.fn().mockResolvedValueOnce(['doc1', 'doc2']),
        } as unknown as DocumentService,
        enrollments: {
          findByApplicantId: vi
            .fn()
            .mockResolvedValueOnce([{ id: 'enrlmnt1' }])
            .mockResolvedValueOnce([]),
          relinkUser: vi.fn().mockResolvedValueOnce(['enrlmnt1']),
          resetCache: vi.fn(),
        } as unknown as EnrollmentService,
        payments: {
          relinkUser: vi.fn().mockResolvedValueOnce(['payment1']),
        } as unknown as PaymentService,
        transactionAuditLog: {
          save: mockSaveAuditLogFn,
        } as unknown as TransactionAuditLogRepository,
        users: {
          deactivate: mockDeactivateUserFn,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', legacyId: validLegacyUser.legacyId }),
          findWithOptions: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(validLegacyUser),
          resetCache: vi.fn(),
          update: mockUpdateUserFn,
        } as unknown as UserService,
        workflowEvents: { create: mockCreateWflwEvtFn } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        legacyUserId: validLegacyUser.id,
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: { id: 'mockUser', legacyId: validLegacyUser.legacyId },
        query: {},
      });

      expect(mockUpdateUserFn).toHaveBeenCalledWith({
        id: 'mockUser',
        legacyId: validLegacyUser.legacyId,
      });
      expect(mockCaseUpdate).toHaveBeenCalledWith(['case1'], { name: 'Mock Name' });
      expect(mockDeactivateUserFn).toHaveBeenCalledWith(validLegacyUser.id);
      expect(mockCreateWflwEvtFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockUser',
        entityType: 'user',
        action: WorkflowAction.LinkLegacyUser,
        previousValue: validLegacyUser.id,
        newValue: 'mockUser',
      });
      expect(mockSaveAuditLogFn).toHaveBeenCalledWith({
        operation: TransactionOperation.LinkLegacyUser,
        status: TransactionStatus.Success,
        input: { id: 'mockUser', legacyUserId: validLegacyUser.id },
        metadata: {
          userId: 'mockUser',
          legacyUserId: validLegacyUser.id,
          legacyId: validLegacyUser.legacyId,
          cases: ['case1'],
          applications: ['app1', 'app2'],
          applicationVersions: ['vers1', 'vers2', 'vers3'],
          documents: ['doc1', 'doc2'],
          enrollments: ['enrlmnt1'],
          payments: ['payment1'],
        },
      });
    });
  });

  describe('when the user and legacy user have conflicting enrollments', () => {
    it("overrides the new user's enrollments with the legacy user's", async () => {
      const mockCreateWflwEvtFn = vi.fn();
      const mockSaveAuditLogFn = vi.fn();
      const mockUpdateUserFn = vi.fn();
      const mockCaseUpdate = vi.fn();
      const mockDeactivateEnrlmntFn = vi.fn();
      const mockDeactivateUserFn = vi.fn();
      const operation = new LinkLegacyUserOperation({
        applications: {
          findBySubmitterId: vi.fn().mockResolvedValueOnce([{ caseId: 'case1' }]),
          relinkUser: vi.fn().mockResolvedValueOnce(['app1', 'app2']),
          resetCache: vi.fn(),
        } as unknown as ApplicationService,
        applicationVersions: {
          relinkUser: vi.fn().mockResolvedValueOnce(['vers1', 'vers2', 'vers3']),
        } as unknown as ApplicationVersionService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        documents: {
          relinkUser: vi.fn().mockResolvedValueOnce(['doc1', 'doc2']),
        } as unknown as DocumentService,
        enrollments: {
          deactivate: mockDeactivateEnrlmntFn,
          findByApplicantId: vi
            .fn()
            .mockResolvedValueOnce([{ id: 'enrlmnt1', programId: 'program1' }])
            .mockResolvedValueOnce([
              { id: 'enrlmnt2', programId: 'program1' },
              { id: 'enrlmnt3', programId: 'program2' },
            ]),
          relinkUser: vi.fn().mockResolvedValueOnce(['enrlmnt1']),
          resetCache: vi.fn(),
        } as unknown as EnrollmentService,
        payments: {
          relinkUser: vi.fn().mockResolvedValueOnce(['payment1']),
        } as unknown as PaymentService,
        transactionAuditLog: {
          save: mockSaveAuditLogFn,
        } as unknown as TransactionAuditLogRepository,
        users: {
          deactivate: mockDeactivateUserFn,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', legacyId: validLegacyUser.legacyId }),
          findWithOptions: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(validLegacyUser),
          resetCache: vi.fn(),
          update: mockUpdateUserFn,
        } as unknown as UserService,
        workflowEvents: { create: mockCreateWflwEvtFn } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        legacyUserId: validLegacyUser.id,
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: { id: 'mockUser', legacyId: validLegacyUser.legacyId },
        query: {},
      });

      expect(mockUpdateUserFn).toHaveBeenCalledWith({
        id: 'mockUser',
        legacyId: validLegacyUser.legacyId,
      });
      expect(mockCaseUpdate).toHaveBeenCalledWith(['case1'], { name: 'Mock Name' });
      expect(mockDeactivateEnrlmntFn).toHaveBeenCalledWith([
        { applicantId: 'mockUser', programId: 'program1' },
      ]);
      expect(mockDeactivateUserFn).toHaveBeenCalledWith(validLegacyUser.id);
      expect(mockCreateWflwEvtFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockUser',
        entityType: 'user',
        action: WorkflowAction.LinkLegacyUser,
        previousValue: validLegacyUser.id,
        newValue: 'mockUser',
      });
      expect(mockSaveAuditLogFn).toHaveBeenCalledWith({
        operation: TransactionOperation.LinkLegacyUser,
        status: TransactionStatus.Success,
        input: { id: 'mockUser', legacyUserId: validLegacyUser.id },
        metadata: {
          userId: 'mockUser',
          legacyUserId: validLegacyUser.id,
          legacyId: validLegacyUser.legacyId,
          cases: ['case1'],
          applications: ['app1', 'app2'],
          applicationVersions: ['vers1', 'vers2', 'vers3'],
          documents: ['doc1', 'doc2'],
          enrollments: ['enrlmnt1'],
          payments: ['payment1'],
        },
      });
    });
  });
});

import { ApplicantTypeRole, NotificationType } from '@bybeam/platform-types';
import { mockLoginToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import {
  AddressService,
  ApplicantProfileService,
  ApplicationService,
  CaseService,
  ExternalNotificationService,
  IdentityService,
  PartnerService,
  UserService,
  WorkflowEventService,
} from '@platform-api/@types/services.js';
import UpdateUserProfileOperation from './UpdateUserProfileOperation.js';

describe('UpdateUserProfileOperation', () => {
  describe('when the user is not found', () => {
    it('returns a 500 error', async () => {
      const operation = new UpdateUserProfileOperation({
        addresses: {} as unknown as AddressService,
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: { findById: vi.fn().mockResolvedValueOnce(undefined) } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        identity: {} as unknown as IdentityService,
        applicantProfiles: {} as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const { metadata } = await operation.run(mockLoginToken, {
        name: 'mockName',
        phone: '1112223333',
      });

      expect(metadata).toEqual({
        status: 500,
        message: 'Internal Server Error',
        errors: ['Internal Server Error'],
      });
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: new Error('No user found') },
        'UpdateUserProfileOperation: unexpected error >',
      );
    });
  });

  describe('when the phone field is being updated', () => {
    it("updates the user's phone", async () => {
      const mockResponse = { id: mockLoginToken.userId, name: 'Mock Name', phone: '1112223333' };
      const mockUpdate = vi.fn().mockResolvedValueOnce(mockResponse);
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValueOnce([]);
      const mockIdentityUserUpdate = vi.fn().mockResolvedValueOnce({ message: 'Updated' });
      const mockNotify = vi.fn();
      const operation = new UpdateUserProfileOperation({
        addresses: {} as unknown as AddressService,
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({
              id: mockLoginToken.userId,
              name: 'Mock Name',
              phone: '00000000000',
            })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: { notify: mockNotify } as unknown as ExternalNotificationService,
        identity: { updateUser: mockIdentityUserUpdate } as unknown as IdentityService,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockLoginToken, {
        name: 'Mock Name',
        phone: '1112223333',
      });
      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: mockLoginToken.userId },
        record: { id: mockLoginToken.userId, name: 'Mock Name', phone: '1112223333' },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        id: mockLoginToken.userId,
        name: 'Mock Name',
        phone: '1112223333',
        email: undefined,
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: mockLoginToken.userId,
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockNotify).not.toHaveBeenCalled();
      expect(mockIdentityUserUpdate).toHaveBeenCalledWith({
        identityUserId: 'mockIdentityUserId',
        partnerId: 'mockPartnerId',
        userIdentifier: 'identityUserId',
        name: 'Mock Name',
        phone: '1112223333',
        email: undefined,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });
    });
    it('sends welcome message if there was previously no phone number', async () => {
      const mockResponse = { id: mockLoginToken.userId, name: 'Mock Name', phone: '1112223333' };
      const mockUpdate = vi.fn().mockResolvedValueOnce(mockResponse);
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValueOnce([]);
      const mockIdentityUserUpdate = vi.fn().mockResolvedValueOnce({ message: 'Updated' });
      const mockNotify = vi.fn();
      const operation = new UpdateUserProfileOperation({
        addresses: {} as unknown as AddressService,
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({
              id: mockLoginToken.userId,
              name: 'Mock Name',
            })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: { notify: mockNotify } as unknown as ExternalNotificationService,
        identity: { updateUser: mockIdentityUserUpdate } as unknown as IdentityService,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockLoginToken, {
        name: 'Mock Name',
        phone: '1112223333',
      });
      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: mockLoginToken.userId },
        record: { id: mockLoginToken.userId, name: 'Mock Name', phone: '1112223333' },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        id: mockLoginToken.userId,
        name: 'Mock Name',
        phone: '1112223333',
        email: undefined,
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: mockLoginToken.userId,
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockIdentityUserUpdate).toHaveBeenCalledWith({
        identityUserId: 'mockIdentityUserId',
        partnerId: 'mockPartnerId',
        userIdentifier: 'identityUserId',
        name: 'Mock Name',
        phone: '1112223333',
        email: undefined,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });
      expect(mockNotify).toHaveBeenCalledWith({
        type: NotificationType.WelcomeMessage,
        context: { partnerId: 'mockPartnerId' },
        params: {},
        recipients: [
          {
            userId: mockLoginToken.userId,
            communicationPreferences: {
              sms: true,
            },
          },
        ],
        tags: { partner_id: 'mockPartnerId' },
      });
    });
  });

  describe('when the name field is updated', () => {
    it("updates the user's details and and changes the name of all the user's related cases", async () => {
      const mockResponse = { id: mockLoginToken.userId, name: 'New Name', phone: '1112223333' };
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: mockLoginToken.userId,
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
        applicantType: { role: ApplicantTypeRole.FirstParty },
      };
      const mockUpdate = vi.fn().mockResolvedValue(mockResponse);
      const mockCaseUpdate = vi.fn();
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValue([mockApplicantProfile]);
      const mockIdentityUserUpdate = vi.fn().mockResolvedValueOnce({ message: 'Updated' });
      const operation = new UpdateUserProfileOperation({
        addresses: {} as unknown as AddressService,
        applications: {
          findBySubmitterId: vi
            .fn()
            .mockResolvedValueOnce([{ caseId: 'case1' }, { caseId: 'case2' }]),
        } as unknown as ApplicationService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: mockLoginToken.userId, name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: { notify: vi.fn() } as unknown as ExternalNotificationService,
        identity: { updateUser: mockIdentityUserUpdate } as unknown as IdentityService,
        applicantProfiles: {
          findWithOptions: vi.fn().mockResolvedValueOnce(mockApplicantProfile),
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockLoginToken, {
        name: 'New Name',
        phone: '1112223333',
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: mockLoginToken.userId },
        record: {
          id: mockLoginToken.userId,
          name: 'New Name',
          phone: '1112223333',
        },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        id: mockLoginToken.userId,
        name: 'New Name',
        phone: '1112223333',
        email: undefined,
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: mockLoginToken.userId,
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockCaseUpdate).toHaveBeenCalledWith(['case1', 'case2'], { name: 'New Name' });
      expect(mockIdentityUserUpdate).toHaveBeenCalledWith({
        identityUserId: 'mockIdentityUserId',
        partnerId: 'mockPartnerId',
        userIdentifier: 'identityUserId',
        name: 'New Name',
        phone: '1112223333',
        email: undefined,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });
    });

    it('does not update the case names if the user is a Third Party Applicant', async () => {
      const mockResponse = { id: mockLoginToken.userId, name: 'New Name', phone: '1112223333' };
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: mockLoginToken.userId,
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
        applicantType: { role: ApplicantTypeRole.ThirdParty },
      };
      const mockUpdate = vi.fn().mockResolvedValue(mockResponse);
      const mockCaseUpdate = vi.fn();
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValue([mockApplicantProfile]);
      const mockIdentityUserUpdate = vi.fn().mockResolvedValueOnce({ message: 'Updated' });
      const operation = new UpdateUserProfileOperation({
        addresses: {} as unknown as AddressService,
        applications: {} as unknown as ApplicationService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: mockLoginToken.userId, name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: { notify: vi.fn() } as unknown as ExternalNotificationService,
        identity: { updateUser: mockIdentityUserUpdate } as unknown as IdentityService,
        applicantProfiles: {
          findWithOptions: vi.fn().mockResolvedValueOnce(mockApplicantProfile),
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockLoginToken, {
        name: 'New Name',
        phone: '1112223333',
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: mockLoginToken.userId },
        record: {
          id: mockLoginToken.userId,
          name: 'New Name',
          phone: '1112223333',
        },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        id: mockLoginToken.userId,
        name: 'New Name',
        phone: '1112223333',
        email: undefined,
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: mockLoginToken.userId,
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockCaseUpdate).not.toHaveBeenCalled();
      expect(mockIdentityUserUpdate).toHaveBeenCalledWith({
        identityUserId: 'mockIdentityUserId',
        partnerId: 'mockPartnerId',
        userIdentifier: 'identityUserId',
        name: 'New Name',
        phone: '1112223333',
        email: undefined,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });
    });
  });

  describe('when the address is updated', () => {
    it('upserts an address and saves it to the applicant profile', async () => {
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: mockLoginToken.userId,
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
        applicantType: { role: ApplicantTypeRole.FirstParty },
      };
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValue([mockApplicantProfile]);
      const mockAddressCreate = vi.fn();
      const operation = new UpdateUserProfileOperation({
        addresses: {
          create: mockAddressCreate.mockResolvedValueOnce({ id: 'mockAddressId' }),
        } as unknown as AddressService,
        applications: {
          findBySubmitterId: vi
            .fn()
            .mockResolvedValueOnce([{ caseId: 'case1' }, { caseId: 'case2' }]),
        } as unknown as ApplicationService,
        cases: { update: vi.fn() } as unknown as CaseService,
        users: {
          update: vi.fn(),
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: mockLoginToken.userId, name: 'Mock Name' })
            .mockResolvedValueOnce({ id: mockLoginToken.userId, name: 'Mock Name' }),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        identity: { updateUser: vi.fn() } as unknown as IdentityService,
        applicantProfiles: {
          findWithOptions: vi.fn().mockResolvedValueOnce(mockApplicantProfile),
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: {} as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockLoginToken, {
        name: 'Mock Name',
        mailingAddress: {
          addressLine1: '650 S Town Center Dr',
          city: 'Las Vegas',
          state: 'NV',
          zip: '89144',
        },
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: mockLoginToken.userId },
        record: {
          id: mockLoginToken.userId,
          name: 'Mock Name',
        },
        query: {},
      });
      expect(mockAddressCreate).toHaveBeenCalledWith({
        addressLine1: '650 S Town Center Dr',
        city: 'Las Vegas',
        state: 'NV',
        zip: '89144',
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: mockLoginToken.userId,
          mailingAddressId: 'mockAddressId',
        },
      ]);
    });
  });
});

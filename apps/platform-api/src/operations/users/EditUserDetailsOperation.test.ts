import { AccountType, ApplicantTypeRole, PayeeType, WorkflowAction } from '@bybeam/platform-types';
import { mockAdminToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import { UpsertBankAccountOperation } from '@platform-api/@types/operations.js';
import {
  ApplicantProfileService,
  ApplicationService,
  CaseService,
  ExternalNotificationService,
  IdentityService,
  PartnerService,
  UserService,
  WorkflowEventService,
} from '@platform-api/@types/services.js';
import EditUserDetailsOperation from './EditUserDetailsOperation.js';

describe('EditUserDetailsOperation', () => {
  describe('when the user is not found', () => {
    it('returns a 500 error', async () => {
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: { findById: vi.fn().mockResolvedValueOnce(undefined) } as unknown as UserService,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
        } as unknown as IdentityService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        upsertBankAccount: {} as UpsertBankAccountOperation,
        applicantProfiles: {} as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const { metadata } = await operation.run(mockAdminToken, {
        id: 'noUser',
        name: 'mockName',
        phone: '**********',
      });

      expect(metadata).toEqual({
        status: 500,
        message: 'Internal Server Error',
        errors: ['Internal Server Error'],
      });
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: new Error('No user found') },
        'EditUserDetailsOperation: unexpected error >',
      );
    });
  });

  describe('when the phone field is being updated', () => {
    it("updates the user's phone", async () => {
      const mockResponse = {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
      };
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValueOnce([]);
      const mockIdentityUpdateUser = vi.fn();
      const mockUpdate = vi.fn().mockResolvedValueOnce({ id: 'mockUser' });
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({
              id: 'mockUser',
              name: 'Mock Name',
              partnerId: 'mockPartnerId',
            })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: mockIdentityUpdateUser,
        } as unknown as IdentityService,
        upsertBankAccount: {} as UpsertBankAccountOperation,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
      });
      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: { id: 'mockUser', name: 'Mock Name', phone: '**********' },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalled();
      expect(mockIdentityUpdateUser).toHaveBeenCalledWith({
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        coreUserId: 'mockUser',
        userIdentifier: 'coreUserId',
        partnerId: 'mockPartnerId',
        email: undefined,
        name: 'Mock Name',
        phone: '**********',
        roles: [],
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: 'mockUser',
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
    });
  });

  describe('when the secondary email field is being updated and another user exists with that email', () => {
    it("logs a warning and updates the user's secondary email", async () => {
      const mockResponse = {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
      };
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: 'mockUser',
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
      };
      const mockIdentityUpdateUser = vi.fn();
      const mockUpdate = vi.fn().mockResolvedValueOnce({ id: 'mockUser' });
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValueOnce([mockApplicantProfile]);
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          findWithOptions: vi.fn().mockResolvedValueOnce({
            id: 'secondaryUser',
            name: 'Secondary User',
            email: '<EMAIL>',
          }),
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({
              id: 'mockUser',
              name: 'Mock Name',
              partnerId: 'mockPartnerId',
            })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: mockIdentityUpdateUser,
        } as unknown as IdentityService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        upsertBankAccount: {} as unknown as UpsertBankAccountOperation,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
        secondaryEmail: '<EMAIL>',
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: {
          id: 'mockUser',
          name: 'Mock Name',
          phone: '**********',
        },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalled();
      expect(mockIdentityUpdateUser).toHaveBeenCalledWith({
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        coreUserId: 'mockUser',
        userIdentifier: 'coreUserId',
        partnerId: 'mockPartnerId',
        email: undefined,
        name: 'Mock Name',
        phone: '**********',
        roles: [],
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: 'mockUser',
          secondaryEmail: '<EMAIL>',
          taxId: undefined,
        },
      ]);
    });
  });

  describe('when the name field is updated', () => {
    it("updates the user's details and and changes the name of all the user's related cases", async () => {
      const mockResponse = { id: 'mockUser', name: 'New Name', phone: '**********' };
      const mockIdentityUpdateUser = vi.fn();
      const mockUpdate = vi.fn().mockResolvedValue(mockResponse);
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: 'mockUser',
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
        applicantType: { role: ApplicantTypeRole.FirstParty },
      };
      const mockCaseUpdate = vi.fn();
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValue([mockApplicantProfile]);
      const operation = new EditUserDetailsOperation({
        applications: {
          findBySubmitterId: vi
            .fn()
            .mockResolvedValueOnce([{ caseId: 'case1' }, { caseId: 'case2' }]),
        } as unknown as ApplicationService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        users: {
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: mockIdentityUpdateUser,
        } as unknown as IdentityService,
        upsertBankAccount: {} as unknown as UpsertBankAccountOperation,
        applicantProfiles: {
          findWithOptions: vi.fn().mockResolvedValueOnce(mockApplicantProfile),
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'New Name',
        phone: '**********',
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: {
          id: 'mockUser',
          name: 'New Name',
          phone: '**********',
        },
        query: {},
      });
      expect(mockUpdate).toHaveBeenCalled();
      expect(mockIdentityUpdateUser).toHaveBeenCalled();
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: 'mockUser',
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockCaseUpdate).toHaveBeenCalledWith(['case1', 'case2'], { name: 'New Name' });
    });

    it('does not update the case names if the user is a Third Party Applicant', async () => {
      const mockResponse = { id: 'mockUser', name: 'New Name', phone: '**********' };
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: 'mockUser',
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
        applicantType: { role: ApplicantTypeRole.ThirdParty },
      };
      const mockCaseUpdate = vi.fn();
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValue([mockApplicantProfile]);
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: { update: mockCaseUpdate } as unknown as CaseService,
        users: {
          update: vi.fn().mockResolvedValueOnce({ id: 'mockUser' }),
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: vi.fn(),
        } as unknown as IdentityService,
        upsertBankAccount: {} as unknown as UpsertBankAccountOperation,
        applicantProfiles: {
          findWithOptions: vi.fn().mockResolvedValueOnce(mockApplicantProfile),
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      const resp = await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'New Name',
        phone: '**********',
      });

      expect(resp).toEqual({
        metadata: { status: 200, message: 'OK', id: 'mockUser' },
        record: {
          id: 'mockUser',
          name: 'New Name',
          phone: '**********',
        },
        query: {},
      });
      expect(mockApplicantProfileUpdate).toHaveBeenCalledWith([
        {
          userId: 'mockUser',
          secondaryEmail: undefined,
          taxId: undefined,
        },
      ]);
      expect(mockCaseUpdate).not.toHaveBeenCalled();
    });
  });

  describe('when bank account details are being updated', () => {
    describe('when the account upsert fails', () => {
      const mockResponse = {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
        secondaryEmail: '<EMAIL>',
      };
      const mockUpdate = vi.fn();
      const mockIdentityUpdateUser = vi.fn();
      const mockApplicantProfileUpdate = vi.fn();
      const mockUpsertBankAccountFn = vi
        .fn()
        .mockResolvedValueOnce({ metadata: { status: 400, message: 'Bad Request' } });
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          findWithOptions: vi.fn().mockResolvedValueOnce({
            id: 'secondaryUser',
            name: 'Secondary User',
            email: '<EMAIL>',
          }),
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        upsertBankAccount: { run: mockUpsertBankAccountFn } as UpsertBankAccountOperation,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: mockIdentityUpdateUser,
        } as unknown as IdentityService,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      it('returns an error response and exits without making any updates', async () => {
        const resp = await operation.run(mockAdminToken, {
          id: 'mockUser',
          name: 'Mock Name',
          phone: '**********',
          secondaryEmail: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: '12345',
            routingNumber: '09876',
          },
        });

        expect(resp).toEqual({ metadata: { status: 400, message: 'Bad Request' }, query: {} });
        expect(mockUpdate).not.toHaveBeenCalled();
        expect(mockIdentityUpdateUser).not.toHaveBeenCalled();
        expect(mockApplicantProfileUpdate).not.toHaveBeenCalled();
        expect(mockUpsertBankAccountFn).toHaveBeenCalledWith(mockAdminToken, {
          payee: { payeeType: PayeeType.User, id: 'mockUser' },
          update: {
            name: 'Mock Name',
            bankAccount: {
              accountType: AccountType.Checking,
              accountNumber: '12345',
              routingNumber: '09876',
            },
            email: undefined,
          },
        });
      });
    });

    describe('when the account upsert succeeds', () => {
      const mockResponse = {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
        secondaryEmail: '<EMAIL>',
      };
      const mockApplicantProfile = {
        id: 'mockProfileId',
        userId: 'mockUser',
        secondaryEmail: '<EMAIL>',
        taxId: undefined,
        secondaryPhone: undefined,
      };
      const mockUpdate = vi.fn().mockResolvedValueOnce({ id: 'mockUser' });
      const mockApplicantProfileUpdate = vi.fn().mockResolvedValueOnce([mockApplicantProfile]);
      const mockUpsertBankAccountFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });
      const mockIdentityUpdateUser = vi.fn();
      const operation = new EditUserDetailsOperation({
        applications: {} as unknown as ApplicationService,
        cases: {} as unknown as CaseService,
        users: {
          findWithOptions: vi.fn().mockResolvedValueOnce({
            id: 'secondaryUser',
            name: 'Secondary User',
            email: '<EMAIL>',
          }),
          update: mockUpdate,
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
            .mockResolvedValueOnce(mockResponse),
          resetCache: vi.fn(),
        } as unknown as UserService,
        externalNotifications: {} as unknown as ExternalNotificationService,
        upsertBankAccount: { run: mockUpsertBankAccountFn } as UpsertBankAccountOperation,
        identity: {
          doesUserExist: vi.fn().mockResolvedValueOnce({ userExists: false }),
          updateUser: mockIdentityUpdateUser,
        } as unknown as IdentityService,
        applicantProfiles: {
          update: mockApplicantProfileUpdate,
        } as unknown as ApplicantProfileService,
        partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
        workflowEvents: {
          create: vi.fn(),
        } as unknown as WorkflowEventService,
      });

      it('continues to update the rest of the fields', async () => {
        const resp = await operation.run(mockAdminToken, {
          id: 'mockUser',
          name: 'Mock Name',
          phone: '**********',
          secondaryEmail: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: '12345',
            routingNumber: '09876',
          },
        });

        expect(resp).toEqual({
          metadata: { status: 200, message: 'OK', id: 'mockUser' },
          record: {
            ...mockResponse,
            bankAccount: {
              accountType: AccountType.Checking,
              accountNumber: '12345',
              routingNumber: '09876',
            },
          },
          query: {},
        });
        expect(mockUpdate).toHaveBeenCalled();
        expect(mockIdentityUpdateUser).toHaveBeenCalled();
        expect(mockApplicantProfileUpdate).toHaveBeenCalled();
        expect(mockUpsertBankAccountFn).toHaveBeenCalledWith(mockAdminToken, {
          payee: { payeeType: PayeeType.User, id: 'mockUser' },
          update: {
            name: 'Mock Name',
            bankAccount: {
              accountType: AccountType.Checking,
              accountNumber: '12345',
              routingNumber: '09876',
            },
            email: undefined,
          },
        });
      });
    });
  });

  describe('when input is valid', () => {
    const mockResponse = { id: 'mockUser', name: 'Mock Name', phone: '**********' };
    const mockUpdate = vi.fn().mockResolvedValueOnce({ id: 'mockUser' });
    const mockWorkflowCreateFn = vi.fn();
    const mockFindUser = vi.fn();
    const mockIdentityUpdateUser = vi.fn();
    const operation = new EditUserDetailsOperation({
      applications: {} as unknown as ApplicationService,
      cases: {} as unknown as CaseService,
      users: {
        update: mockUpdate,
        findById: mockFindUser,
        resetCache: vi.fn(),
      } as unknown as UserService,
      externalNotifications: {} as unknown as ExternalNotificationService,
      upsertBankAccount: {} as unknown as UpsertBankAccountOperation,
      identity: {
        doesUserExist: vi.fn().mockResolvedValue({ userExists: false }),
        updateUser: mockIdentityUpdateUser,
      } as unknown as IdentityService,
      applicantProfiles: {
        update: vi.fn().mockResolvedValue([]),
      } as unknown as ApplicantProfileService,
      partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) } as unknown as PartnerService,
      workflowEvents: {
        create: mockWorkflowCreateFn,
      } as unknown as WorkflowEventService,
    });

    it('Should create a workflow event', async () => {
      mockFindUser
        .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
        .mockResolvedValueOnce(mockResponse);
      await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
      });
      expect(mockWorkflowCreateFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockUser',
        entityType: 'user',
        action: WorkflowAction.UserUpdate,
        previousValue: JSON.stringify({ name: 'Mock Name' }),
        newValue: JSON.stringify({ name: 'Mock Name', phone: '**********' }),
        details: '',
      });
    });
    it('Should create a workflow event with details if taxId has been changed', async () => {
      mockUpdate.mockResolvedValueOnce({ ...mockResponse, taxId: 'mockTaxId' });
      mockFindUser
        .mockResolvedValueOnce({ id: 'mockUser', name: 'Mock Name' })
        .mockResolvedValueOnce(mockResponse);
      await operation.run(mockAdminToken, {
        id: 'mockUser',
        name: 'Mock Name',
        phone: '**********',
        taxId: '*********',
      });
      expect(mockWorkflowCreateFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockUser',
        entityType: 'user',
        action: WorkflowAction.UserUpdate,
        previousValue: JSON.stringify({ name: 'Mock Name' }),
        newValue: JSON.stringify({ name: 'Mock Name', phone: '**********' }),
        details: 'TaxId has been updated.',
      });
    });
  });
});

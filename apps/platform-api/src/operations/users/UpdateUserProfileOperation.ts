import {
  ApplicantTypeR<PERSON>,
  CommunicationChannels,
  MutationResponse,
  NotificationType,
  UpdateUserProfileInput,
  User,
} from '@bybeam/platform-types';
import { LoginToken } from '@platform-api/@types/authentication.js';
import { Services } from '@platform-api/@types/services.js';
import * as response from '../../utils/response.js';
import AbstractOperation from '../AbstractOperation.js';

export default class UpdateUserProfileOperation extends AbstractOperation<
  Pick<
    Services,
    | 'addresses'
    | 'applicantProfiles'
    | 'applications'
    | 'cases'
    | 'externalNotifications'
    | 'identity'
    | 'partners'
    | 'users'
    | 'workflowEvents'
  >,
  UpdateUserProfileInput,
  MutationResponse<User>,
  LoginToken
> {
  public async run(
    { identityUserId, partnerId, userId }: LoginToken,
    {
      name,
      phone,
      email: unsanitizedEmail,
      taxId,
      communicationPreferences,
      applicantTypeId,
      mailingAddress,
    }: UpdateUserProfileInput,
  ): Promise<MutationResponse<User>> {
    this.log('info', `attempting to update user profile details for ${userId}`);

    try {
      const user = await this.dependencies.users.findById(userId);
      if (!user) throw new Error('No user found');
      const shouldSendWelcomeMessage = !!(!user.phone && phone);
      const email = unsanitizedEmail?.toLowerCase();

      await this.dependencies.users.update({
        id: userId,
        name,
        phone,
        email,
        communicationPreferences,
        taxId,
      });

      await this.dependencies.applicantProfiles.update([
        {
          userId: userId,
        },
      ]);

      if (user.name !== name) {
        const applicantProfile = await this.dependencies.applicantProfiles.findWithOptions({
          where: { userId: user.id },
          relations: ['applicantType'],
        });

        if (applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty) {
          const caseIds = (await this.dependencies.applications.findBySubmitterId(userId)).map(
            ({ caseId }) => caseId,
          );
          if (caseIds.length > 0) await this.dependencies.cases.update(caseIds, { name });
        }

        if (applicantTypeId) {
          await this.dependencies.applicantProfiles.update([{ userId: user.id, applicantTypeId }]);
        }
      }

      if (mailingAddress) {
        const { id: mailingAddressId } = await this.dependencies.addresses.create(mailingAddress);
        await this.dependencies.applicantProfiles.update([{ userId, mailingAddressId }]);
      }

      this.dependencies.users.resetCache(userId);

      await this.dependencies.identity.updateUser({
        partnerId,
        userIdentifier: 'identityUserId',
        identityUserId,
        name,
        phone,
        email,
        roles: [],
        _email: 'email',
        _name: 'name',
        _phone: 'phone',
        // deprecated: should be removed later
        _userId: 'userId',
        _gcipUid: 'gcipUid',
        _tenantId: 'tenantId',
      });

      if (shouldSendWelcomeMessage) {
        this.dependencies.externalNotifications.notify({
          type: NotificationType.WelcomeMessage,
          context: { partnerId },
          params: {},
          recipients: [
            { userId: user.id, communicationPreferences: { [CommunicationChannels.SMS]: true } },
          ],
          tags: { partner_id: partnerId },
        });
      }

      return response.success({
        recordId: userId,
        record: await this.dependencies.users.findById(userId),
      });
    } catch (error) {
      this.log('error', 'unexpected error >', { error });
      return response.error();
    }
  }
}

import { MeterProvider } from '@bybeam/infrastructure-lib/instrumentation';
import { EventProducer } from '@platform-api/@types/events.js';
import { Services } from '../@types/services.js';
import buildRepositories from '../repositories/index.js';
import AddressService from './addresses/AddressService.js';
import AdminService from './admins/AdminService.js';
import AnalyticsResourceService from './analytics/AnalyticsResourceService.js';
import ApplicantProfileService from './applicantProfiles/ApplicantProfileService.js';
import ApplicantTypeService from './applicantTypes/ApplicantTypeService.js';
import ApplicationAnswerNotesService from './applicationAnswerNotes/ApplicationAnswerNoteService.js';
import ApplicationAnswerReviewsService from './applicationAnswerReviews/ApplicationAnswerReviewService.js';
import ApplicationAnswerService from './applicationAnswers/ApplicationAnswerService.js';
import ApplicationScoreService from './applicationScoring/ApplicationScoreService.js';
import ApplicationVerificationService from './applicationVerification/ApplicationVerificationService.js';
import ApplicationVersionService from './applicationVersions/ApplicationVersionService.js';
import ApplicationService from './applications/ApplicationService.js';
import AssignmentService from './assignments/AssignmentService.js';
import BulkOperationService from './bulkOperations/BulkOperationService.js';
import CaseParticipantService from './caseParticipants/CaseParticipantService.js';
import CaseTagService from './caseTags/CaseTagService.js';
import CaseService from './cases/CaseService.js';
import ChangelogsService from './changelogs/ChangelogService.js';
import CommentService from './comments/CommentService.js';
import ConfigurationService from './config/ConfigurationService.js';
import DocumentService from './documents/DocumentService.js';
import ActiveLabelService from './documents/doctopus/ActiveLabelService.js';
import PredictionFeedbackService from './documents/doctopus/PredictionFeedbackService.js';
import EligibilityService from './eligibility/EligibilityService.js';
import EnrollmentService from './enrollments/EnrollmentService.js';
import ExternalNotificationsService from './externalNotifications/externalNotificationsService.js';
import getGenerator from './externalNotifications/generators/index.js';
import FulfillmentMetaService from './fulfillmentMeta/FulfillmentMetaService.js';
import FulfillmentService from './fulfillments/FulfillmentService.js';
import FundService from './funds/FundService.js';
import IdentityService from './identity/IdentityService.js';
import IncidentMessageService from './incidentMessages/IncidentMessageService.js';
import IncomeLimitAreaService from './incomeLimitAreas/IncomeLimitAreaService.js';
import InvitationCodeService from './invitationCodes/InvitationCodeService.js';
import LinkAttemptService from './linkAttempts/LinkAttemptService.js';
import MessagingService from './messaging/MessagingService.js';
import NoteService from './notes/NoteService.js';
import NotificationService from './notifications/NotificationService.js';
import OutcomeService from './outcomes/OutcomeService.js';
import PartnerFeatureService from './partnerFeatures/PartnerFeatureService.js';
import PartnerIncidentService from './partnerIncidents/PartnerIncidentService.js';
import PartnerService from './partners/PartnerService.js';
import PartnerWhitelabelingService from './partners/PartnerWhitelabelingService.js';
import PayeeService from './payees/PayeeService.js';
import PaymentPatternService from './paymentPatterns/PaymentPatternService.js';
import PaymentService from './payments/PaymentService.js';
import PresetService from './preset/PresetService.js';
import ProfileAnswerService from './profileAnswers/ProfileAnswerService.js';
import ProgramDocumentService from './programDocuments/ProgramDocumentService.js';
import ProgramFeatureService from './programFeatures/ProgramFeatureService.js';
import ProgramFundService from './programFunds/ProgramFundService.js';
import ProgramReferralService from './programReferral/programReferralService.js';
import ProgramService from './programs/ProgramService.js';
import SavedViewService from './savedViews/SavedViewService.js';
import SearchService from './search/SearchService.js';
import ServiceService from './services/ServiceService.js';
import TagsService from './tags/TagsService.js';
import TaxFormService from './taxForms/TaxFormService.js';
import UserService from './users/UserService.js';
import VendorTypeService from './vendorTypes/VendorTypeService.js';
import VendorService from './vendors/VendorService.js';
import VerificationService from './verification/VerificationService.js';
import WorkflowEventService from './workflowEvent/WorkflowEventService.js';

export const build = (events: EventProducer, meterProvider?: MeterProvider): Services => {
  const repositories = buildRepositories(meterProvider);

  const messaging = new MessagingService(repositories.Pubsub);

  const changelogs = new ChangelogsService(repositories.Changelog);

  const partnerFeatures = new PartnerFeatureService(repositories.PartnerFeature);
  const partners = new PartnerService(repositories.Partner, partnerFeatures);
  const whitelabeling = new PartnerWhitelabelingService(repositories.PartnerWhitelabeling);

  const incidentMessages = new IncidentMessageService(repositories.IncidentMessage);

  const workflowEvents = new WorkflowEventService(repositories.WorkflowEvent);

  const bulkOperations = new BulkOperationService(repositories.BulkOperation, workflowEvents);

  const eligibility = new EligibilityService(repositories.EligibilityConfig);
  const config = new ConfigurationService(repositories.Config);

  const applicantTypes = new ApplicantTypeService(
    repositories.ApplicantType,
    repositories.ProgramApplicantType,
    repositories.Partner,
  );

  const users = new UserService({
    repository: repositories.User,
    identityRepo: repositories.Identity,
  });

  const externalNotifications = new ExternalNotificationsService(
    repositories.Notification,
    { get: getGenerator },
    users,
    config,
  );

  const incomeLimitAreas = new IncomeLimitAreaService(repositories.IncomeLimitArea);
  const programFeatures = new ProgramFeatureService(
    repositories.ProgramFeature,
    repositories.Feature,
  );
  const programFunds = new ProgramFundService(repositories.ProgramFunds);

  const programs = new ProgramService({
    programRepository: repositories.Program,
    labelRepository: repositories.Label,
    notificationTemplateRepository: repositories.NotificationTemplate,
    workflowEventService: workflowEvents,
    configService: config,
  });
  const documents = new DocumentService({
    documentRepository: repositories.Document,
    feedbackRepository: repositories.PredictionFeedback,
    documentTagRepository: repositories.DocumentTag,
    documentFieldRepository: repositories.DocumentField,
    messagingService: messaging,
    partnerService: partners,
    programService: programs,
    summaryRepository: repositories.DocumentSummary,
    uploadRepository: repositories.Upload,
  });
  const programDocuments = new ProgramDocumentService(repositories.ProgramDocument);
  const funds = new FundService({
    repository: repositories.Fund,
    claimRepository: repositories.Claim,
    workflowEventService: workflowEvents,
  });
  const taxForms = new TaxFormService(repositories.TaxForm);

  const profileAnswers = new ProfileAnswerService(repositories.ProfileAnswer);

  const applicantProfiles = new ApplicantProfileService(repositories.ApplicantProfile);

  const assignments = new AssignmentService(repositories.Assignment, repositories.Admin);

  const identity = new IdentityService({ repository: repositories.Identity });

  const admins = new AdminService({
    repository: repositories.Admin,
    assignmentService: assignments,
    identityService: identity,
    userService: users,
    workflowEventService: workflowEvents,
  });

  const addresses = new AddressService(repositories.Address, incomeLimitAreas);
  const applicationAnswers = new ApplicationAnswerService(
    repositories.ApplicationAnswer,
    repositories.LatestApplicationAnswers,
    repositories.ApplicationVersion,
    config,
  );

  const applicationAnswerNotes = new ApplicationAnswerNotesService(
    repositories.ApplicationAnswerNote,
    repositories.Note,
  );

  const applicationAnswerReviews = new ApplicationAnswerReviewsService(
    repositories.ApplicationAnswerReview,
    repositories.Note,
    workflowEvents,
  );

  const applicationVersions = new ApplicationVersionService(
    repositories.ApplicationVersion,
    config,
    applicationAnswers,
  );

  const applications = new ApplicationService(
    repositories.Application,
    applicationAnswers,
    applicationVersions,
  );

  const caseParticipants = new CaseParticipantService(repositories.CaseParticipant);
  const invitationCodes = new InvitationCodeService(repositories.InvitationCode);
  const linkAttempts = new LinkAttemptService(repositories.LinkAttempt);
  const notes = new NoteService(repositories.Note, workflowEvents);
  const caseTags = new CaseTagService(
    repositories.CaseTag,
    repositories.Tag,
    applications,
    messaging,
  );
  const cases = new CaseService(
    repositories.Case,
    repositories.CaseMetadata,
    caseParticipants,
    caseTags,
    documents,
    programs,
    repositories.Linking,
    workflowEvents,
  );
  const applicationScores = new ApplicationScoreService(
    repositories.ApplicationScore,
    applicationVersions,
  );

  const applicationVerifications = new ApplicationVerificationService(
    repositories.ApplicationVerification,
    applicationVersions,
  );

  const vendors = new VendorService(
    repositories.Vendor,
    repositories.VendorType,
    addresses,
    documents,
  );

  const paymentPatterns = new PaymentPatternService(repositories.PaymentPattern, programs);

  const payments = new PaymentService({
    repository: repositories.Payment,
    caseService: cases,
    fulfillmentRepository: repositories.Fulfillment,
    fulfillmentMetaRepository: repositories.FulfillmentMeta,
    identityService: identity,
    messagingService: messaging,
    paymentPatternService: paymentPatterns,
    programFeatureService: programFeatures,
    workflowEventService: workflowEvents,
  });

  const payees = new PayeeService(repositories.Claim, addresses, applicantProfiles);

  const fulfillments = new FulfillmentService(
    repositories.Fulfillment,
    repositories.Claim,
    repositories.Lock,
    repositories.Payment,
  );

  const fulfillmentMeta = new FulfillmentMetaService(repositories.FulfillmentMeta);
  const vendorTypes = new VendorTypeService(repositories.VendorType);
  const outcomes = new OutcomeService(repositories.Outcome);
  const services = new ServiceService(repositories.Service);
  const enrollments = new EnrollmentService(
    repositories.Enrollment,
    repositories.EnrollmentOutcome,
    repositories.EnrollmentService,
    outcomes,
    services,
  );

  const analyticsResources = new AnalyticsResourceService(repositories.AnalyticsResource);

  const programReferrals = new ProgramReferralService({
    repository: repositories.ProgramReferral,
    applicationService: applications,
    externalNotificationsService: externalNotifications,
    programService: programs,
    workflowEventService: workflowEvents,
  });

  const verification = new VerificationService({
    applicationVerificationRepository: repositories.ApplicationVerification,
    eventProducer: events,
    programDocumentService: programDocuments,
    programFeatureService: programFeatures,
    verificationRepository: repositories.Verification,
  });

  const tags = new TagsService(repositories.Tag);
  const comments = new CommentService(repositories.Comment);
  const notifications = new NotificationService(repositories.CoreNotification);
  const savedViews = new SavedViewService(repositories.SavedView);

  const partnerIncidents = new PartnerIncidentService({
    partnerIncidentRepository: repositories.PartnerIncident,
    incidentMessageService: incidentMessages,
    workflowEventService: workflowEvents,
  });

  // Doctopus Services
  const activeLabels = new ActiveLabelService(repositories.Label);
  const predictionFeedback = new PredictionFeedbackService(repositories.PredictionFeedback);

  const search = new SearchService({ repository: repositories.Elasticsearch });

  const presetService = new PresetService({ userService: users, partnerService: partners });

  return {
    admins,
    addresses,
    analyticsResources,
    applications,
    applicationAnswers,
    applicationAnswerNotes,
    applicationAnswerReviews,
    applicationScores,
    applicationVersions,
    applicationVerifications,
    applicantProfiles,
    applicantTypes,
    assignments,
    bulkOperations,
    cases,
    caseParticipants,
    caseTags,
    changelogs,
    config,
    comments,
    documents,
    eligibility,
    externalNotifications,
    enrollments,
    fulfillments,
    fulfillmentMeta,
    funds,
    identity,
    incidentMessages,
    incomeLimitAreas,
    invitationCodes,
    linkAttempts,
    messaging,
    notes,
    notifications,
    outcomes,
    partners,
    partnerIncidents,
    partnerFeatures,
    payees,
    payments,
    paymentPatterns,
    preset: presetService,
    programs,
    programDocuments,
    programFeatures,
    programFunds,
    programReferrals,
    profileAnswers,
    search,
    services,
    savedViews,
    tags,
    taxForms,
    users,
    vendors,
    vendorTypes,
    verification,
    whitelabeling,
    workflowEvents,

    // Doctopus Services
    activeLabels,
    predictionFeedback,
  };
};

import {
  ALLOWED_MIME_TYPES,
  Document,
  UploadDocumentsInput,
  VALID_FILE_TYPES,
  YesNoUnsure,
} from '@bybeam/platform-types';
import { mockAdminToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import { LoginToken } from '@platform-api/@types/authentication.js';
import {
  DocumentFieldRepository,
  DocumentRepository,
  DocumentSummaryRepository,
  DocumentTagRepository,
  PredictionFeedbackRepository,
} from '@platform-api/@types/repositories/index.js';
import { MessagingService } from '@platform-api/@types/services.js';
import { UploadRepository } from '@platform-api/@types/upload.js';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import heic from 'heic-convert';
import { In } from 'typeorm';
import PartnerService from '../partners/PartnerService.js';
import ProgramService from '../programs/ProgramService.js';
import DocumentService, { FILE_TYPE_ERROR_MESSAGE } from './DocumentService.js';

vi.mock('heic-convert');

describe('DocumentService', () => {
  describe('validateFileTypes', () => {
    const mockService = new DocumentService({
      documentRepository: {
        insert: vi.fn().mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
        findBy: vi.fn().mockResolvedValueOnce([
          {
            id: 'mockDoc1',
            filename: 'doc1.pdf',
            documentKey: 'applications/mockAppId/doc1.pdf',
          },
          {
            id: 'mockDoc2',
            filename: 'doc2.pdf',
            documentKey: 'applications/mockAppId/doc2.pdf',
          },
        ]),
        save: vi.fn().mockResolvedValueOnce({
          id: 'mockDocumentId',
          documentKey: 'mockDocumentKey',
          uploaderId: 'mockUploaderId',
          filename: 'mockFileName.jpg',
          mimetype: 'image/jpeg',
          createdAt: new Date('11/01/2022'),
        }),
      } as unknown as DocumentRepository,
      feedbackRepository: {} as unknown as PredictionFeedbackRepository,
      messagingService: {} as unknown as MessagingService,
      partnerService: {
        findById: vi
          .fn()
          .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
      } as unknown as PartnerService,
      programService: {} as unknown as ProgramService,
      summaryRepository: {} as unknown as DocumentSummaryRepository,
      documentTagRepository: {} as unknown as DocumentTagRepository,
      documentFieldRepository: {} as unknown as DocumentFieldRepository,
      uploadRepository: { getPreviewUrl: vi.fn() } as unknown as UploadRepository,
    });
    describe('valid file types', () => {
      test.each(VALID_FILE_TYPES)('should not throw on valid %p file', async (type) => {
        const files = [
          Promise.resolve({
            filename: `doc1.${type}`,
            mimetype: 'application/pdf',
          }),
          Promise.resolve({
            filename: `doc1.${type}`,
            mimetype: 'application/pdf',
          }),
        ] as Promise<FileUpload>[];
        const errorMessage = await mockService.validateFileTypes(files);
        expect(errorMessage).toBeUndefined();
      });
    });
    describe('allowed mimetypes', () => {
      test.each(ALLOWED_MIME_TYPES)('should not throw on valid %p MIME type', async (type) => {
        const files = [
          Promise.resolve({
            filename: 'doc1.jpg',
            mimetype: type,
          }),
          Promise.resolve({
            filename: 'doc2.jpg',
            mimetype: type,
          }),
        ] as Promise<FileUpload>[];
        const errorMessage = await mockService.validateFileTypes(files);
        expect(errorMessage).toBeUndefined();
      });
    });
    describe('errors', () => {
      it('should throw an error with an null byte injection file type', async () => {
        const files = [
          Promise.resolve({ filename: 'doc1.php%001.jpg', mimetype: 'application/x-httpd-php' }),
        ] as Promise<FileUpload>[];
        const errorMessage = await mockService.validateFileTypes(files);
        expect(errorMessage).toBe(FILE_TYPE_ERROR_MESSAGE);
      });
      it('should throw an error with an invalid file name', async () => {
        const files = [
          Promise.resolve({ filename: 'doc1.zip', mimetype: 'application/pdf' }),
        ] as Promise<FileUpload>[];
        const errorMessage = await mockService.validateFileTypes(files);
        expect(errorMessage).toBe(FILE_TYPE_ERROR_MESSAGE);
      });
      it('should throw an error with an invalid MIME type', async () => {
        const files = [
          Promise.resolve({ filename: 'doc1.docx', mimetype: 'application/gzip' }),
        ] as Promise<FileUpload>[];
        const errorMessage = await mockService.validateFileTypes(files);
        expect(errorMessage).toBe(FILE_TYPE_ERROR_MESSAGE);
      });
    });
  });

  describe('getPreviewUrl', () => {
    describe('when the partner does not exist', () => {
      it('throws', async () => {
        const findPartner = vi.fn();
        const getPreview = vi.fn();

        const mockService = new DocumentService({
          documentRepository: {
            insert: vi.fn().mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
            save: vi.fn().mockResolvedValueOnce({
              id: 'mockDocumentId',
              documentKey: 'mockDocumentKey',
              uploaderId: 'mockUploaderId',
              filename: 'mockFileName.jpg',
              mimetype: 'image/jpeg',
              createdAt: new Date('11/01/2022'),
            }),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {
            findById: findPartner.mockRejectedValueOnce(new Error('No Partner!')),
          } as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: { getPreviewUrl: getPreview } as unknown as UploadRepository,
        });

        await expect(
          mockService.getPreviewUrl(
            { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
            {
              id: 'mockDocId',
            } as unknown as Document,
          ),
        ).rejects.toThrow();

        expect(findPartner).toHaveBeenCalledWith('mockPartnerId');
        expect(getPreview).not.toHaveBeenCalled();
      });
    });

    describe('when the partner exists', () => {
      it('gets the preview url', async () => {
        const getPreview = vi.fn();

        const mockService = new DocumentService({
          documentRepository: {
            insert: vi.fn().mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
            save: vi.fn().mockResolvedValueOnce({
              id: 'mockDocumentId',
              documentKey: 'mockDocumentKey',
              uploaderId: 'mockUploaderId',
              filename: 'mockFileName.jpg',
              mimetype: 'image/jpeg',
              createdAt: new Date('11/01/2022'),
            }),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {
            findById: vi
              .fn()
              .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
          } as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {
            getPreviewUrl: getPreview.mockResolvedValueOnce('mockUrl'),
          } as unknown as UploadRepository,
        });

        const resp = await mockService.getPreviewUrl(
          { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
          { id: 'mockDocId' } as unknown as Document,
        );

        expect(getPreview).toHaveBeenCalledWith({
          partner: { id: 'mockPartner', externalId: 'partner-external' },
          document: { id: 'mockDocId' },
        });
        expect(resp).toEqual('mockUrl');
      });
    });

    describe('when getting the preview URL fails', () => {
      it('returns an empty string', async () => {
        const getPreview = vi.fn();
        const mockService = new DocumentService({
          documentRepository: {
            insert: vi.fn().mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
            save: vi.fn().mockResolvedValueOnce({
              id: 'mockDocumentId',
              documentKey: 'mockDocumentKey',
              uploaderId: 'mockUploaderId',
              filename: 'mockFileName.jpg',
              mimetype: 'image/jpeg',
              createdAt: new Date('11/01/2022'),
            }),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {
            findById: vi
              .fn()
              .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
          } as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {
            getPreviewUrl: getPreview.mockRejectedValueOnce(new Error('it failed!')),
          } as unknown as UploadRepository,
        });

        const resp = await mockService.getPreviewUrl(
          { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
          {
            id: 'mockDocId',
          } as unknown as Document,
        );

        expect(resp).toEqual('');
        expect(mockLogger.error).toHaveBeenCalledWith(
          { error: new Error('it failed!') },
          'DocumentService.getPreviewUrl: unexpected error >',
        );
      });
    });
  });

  describe('upload', () => {
    it('should sanitize the filenames', async () => {
      const insert = vi.fn();
      const upload = vi.fn();
      const mockService = new DocumentService({
        documentRepository: {
          insert: insert.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
          findBy: vi.fn().mockResolvedValueOnce([
            {
              id: 'mockDoc1',
              filename: '“img src=x onerror=prompt(“XSS”).jpg',
              documentKey: 'applications/mockAppId/“img src=x onerror=prompt(“XSS”).jpg',
            },
          ]),
          save: vi.fn().mockResolvedValueOnce({
            id: 'mockDocumentId',
            documentKey: 'mockDocumentKey',
            uploaderId: 'mockUploaderId',
            filename: '“img src=x onerror=prompt(“XSS”).jpg',
            mimetype: 'image/jpeg',
            createdAt: new Date('11/01/2022'),
          }),
        } as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {
          hasFeature: vi.fn().mockResolvedValueOnce(false),
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
        } as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {
          upload: upload.mockResolvedValueOnce([
            {
              documentKey: 'applications/mockAppId/“img src=x onerror=prompt(“XSS”).jpg',
              filename: '“img src=x onerror=prompt(“XSS”).jpg',
            },
          ]),
        } as unknown as UploadRepository,
      });

      const resp = await mockService.upload(
        { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
        {
          relation: { type: 'application', id: 'mockAppId' },
          files: [
            Promise.resolve({
              filename: '“><img src=x onerror=prompt(“XSS”)>.jpg',
              mimetype: 'application/pdf',
              createReadStream: () =>
                (async function* () {
                  yield Buffer.from('mock file content');
                })(),
              encoding: '7bit',
            } as unknown as FileUpload),
          ],
        } as UploadDocumentsInput,
      );

      expect(upload).toHaveBeenCalledWith({
        partner: { id: 'mockPartner', externalId: 'partner-external' },
        prefix: 'applications/mockAppId',
        files: [
          {
            filename: '“img src=x onerror=prompt(“XSS”).jpg',
            mimetype: 'application/pdf',
            createReadStream: expect.any(Function),
            encoding: '7bit',
          },
        ],
      });
      expect(insert).toHaveBeenCalledWith([
        {
          filename: '“img src=x onerror=prompt(“XSS”).jpg',
          documentKey: 'applications/mockAppId/“img src=x onerror=prompt(“XSS”).jpg',
          uploaderId: 'mockUserId',
          sha256: expect.any(String),
        },
      ]);
      expect(resp).toEqual([
        {
          id: 'mockDoc1',
          documentKey: 'applications/mockAppId/“img src=x onerror=prompt(“XSS”).jpg',
          filename: '“img src=x onerror=prompt(“XSS”).jpg',
        },
      ]);
    });
    describe('when the upload is successful', () => {
      it('should upload documents and save them', async () => {
        const insert = vi.fn();
        const upload = vi.fn();
        const mockService = new DocumentService({
          documentRepository: {
            insert: insert.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
            save: vi.fn().mockResolvedValueOnce({
              id: 'mockDocumentId',
              documentKey: 'mockDocumentKey',
              uploaderId: 'mockUploaderId',
              filename: 'mockFileName.jpg',
              mimetype: 'image/jpeg',
              createdAt: new Date('11/01/2022'),
            }),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {
            hasFeature: vi.fn().mockResolvedValueOnce(false),
            findById: vi
              .fn()
              .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
          } as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {
            upload: upload.mockResolvedValueOnce([
              { filename: 'doc1.pdf', documentKey: 'applications/mockAppId/doc1.pdf' },
              { filename: 'doc2.pdf', documentKey: 'applications/mockAppId/doc2.pdf' },
            ]),
          } as unknown as UploadRepository,
        });

        const mockFileUpload1 = {
          filename: 'doc1.pdf',
          mimetype: 'application/pdf',
          createReadStream: () =>
            (async function* () {
              yield Buffer.from('mock file content 1');
            })(),
          encoding: '7bit',
        } as unknown as FileUpload;

        const mockFileUpload2 = {
          filename: 'doc2.pdf',
          mimetype: 'application/pdf',
          createReadStream: () =>
            (async function* () {
              yield Buffer.from('mock file content 2');
            })(),
          encoding: '7bit',
        } as unknown as FileUpload;

        const resp = await mockService.upload(
          { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
          {
            relation: { type: 'application', id: 'mockAppId' },
            files: [Promise.resolve(mockFileUpload1), Promise.resolve(mockFileUpload2)],
          } as UploadDocumentsInput,
        );

        expect(upload).toHaveBeenCalledWith({
          partner: { id: 'mockPartner', externalId: 'partner-external' },
          prefix: 'applications/mockAppId',
          files: [
            {
              filename: 'doc1.pdf',
              mimetype: 'application/pdf',
              createReadStream: expect.any(Function),
              encoding: '7bit',
            },
            {
              filename: 'doc2.pdf',
              mimetype: 'application/pdf',
              createReadStream: expect.any(Function),
              encoding: '7bit',
            },
          ],
        });
        expect(insert).toHaveBeenCalledWith([
          {
            filename: 'doc1.pdf',
            documentKey: 'applications/mockAppId/doc1.pdf',
            uploaderId: 'mockUserId',
            sha256: expect.any(String),
          },
          {
            filename: 'doc2.pdf',
            documentKey: 'applications/mockAppId/doc2.pdf',
            uploaderId: 'mockUserId',
            sha256: expect.any(String),
          },
        ]);
        expect(resp).toEqual([
          { id: 'mockDoc1', filename: 'doc1.pdf', documentKey: 'applications/mockAppId/doc1.pdf' },
          { id: 'mockDoc2', filename: 'doc2.pdf', documentKey: 'applications/mockAppId/doc2.pdf' },
        ]);
      });
    });
    describe('when the upload is successful and the partner has classify enabled', () => {
      it('should upload documents and publish a document upload message', async () => {
        process.env.DOC_UPLOAD_TOPIC = 'mock-topic-name';
        const mockPublisher = vi.fn();
        const insert = vi.fn();
        const upload = vi.fn();
        const mockService = new DocumentService({
          documentRepository: {
            insert: insert.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: { publishBulk: mockPublisher } as unknown as MessagingService,
          partnerService: {
            hasFeature: vi.fn().mockResolvedValueOnce(true),
            findById: vi
              .fn()
              .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
          } as unknown as PartnerService,
          programService: {
            findById: vi.fn().mockResolvedValueOnce({ config: {} }),
          } as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {
            upload: upload.mockResolvedValueOnce([
              { filename: 'doc1.pdf', documentKey: 'applications/mockAppId/doc1.pdf' },
              { filename: 'doc2.pdf', documentKey: 'applications/mockAppId/doc2.pdf' },
            ]),
          } as unknown as UploadRepository,
        });

        const mockFileUpload1 = {
          filename: 'doc1.pdf',
          mimetype: 'application/pdf',
          createReadStream: () =>
            (async function* () {
              yield Buffer.from('mock file content 1');
            })(),
          encoding: '7bit',
        } as unknown as FileUpload;

        const mockFileUpload2 = {
          filename: 'doc2.pdf',
          mimetype: 'application/pdf',
          createReadStream: () =>
            (async function* () {
              yield Buffer.from('mock file content 2');
            })(),
          encoding: '7bit',
        } as unknown as FileUpload;

        const resp = await mockService.upload(
          { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
          {
            relation: { type: 'case', id: 'mockCaseId' },
            files: [Promise.resolve(mockFileUpload1), Promise.resolve(mockFileUpload2)],
            programId: 'mockProgramId',
          } as UploadDocumentsInput,
        );

        expect(upload).toHaveBeenCalledWith({
          partner: { id: 'mockPartner', externalId: 'partner-external' },
          prefix: 'cases/mockCaseId',
          files: [
            {
              filename: 'doc1.pdf',
              mimetype: 'application/pdf',
              createReadStream: expect.any(Function),
              encoding: '7bit',
            },
            {
              filename: 'doc2.pdf',
              mimetype: 'application/pdf',
              createReadStream: expect.any(Function),
              encoding: '7bit',
            },
          ],
        });
        expect(insert).toHaveBeenCalledWith([
          {
            filename: 'doc1.pdf',
            documentKey: 'applications/mockAppId/doc1.pdf',
            uploaderId: 'mockUserId',
            sha256: expect.any(String),
          },
          {
            filename: 'doc2.pdf',
            documentKey: 'applications/mockAppId/doc2.pdf',
            uploaderId: 'mockUserId',
            sha256: expect.any(String),
          },
        ]);
        expect(mockPublisher).toHaveBeenCalledWith({
          topicName: 'document-upload-[env]',
          messages: [{ document_id: 'mockDoc1' }, { document_id: 'mockDoc2' }],
        });
        expect(resp).toEqual([
          { id: 'mockDoc1', filename: 'doc1.pdf', documentKey: 'applications/mockAppId/doc1.pdf' },
          { id: 'mockDoc2', filename: 'doc2.pdf', documentKey: 'applications/mockAppId/doc2.pdf' },
        ]);
      });
    });

    describe('when the file is ".hei[c|f]"', () => {
      describe('when the conversion is successful', () => {
        it('should upload and save the converted image', async () => {
          const mockIterator = (async function* () {
            yield Buffer.from('pixels');
            yield Buffer.from('morepixels');
          })();
          const mockHeicConvert = heic as vi.mockedFunction<typeof heic>;
          mockHeicConvert
            .mockResolvedValueOnce(new ArrayBuffer(10))
            .mockResolvedValueOnce(new ArrayBuffer(10));
          const mockUploadFn = vi.fn();
          const mockInsertFn = vi.fn();
          const mockService = new DocumentService({
            documentRepository: {
              insert: mockInsertFn.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
              findBy: vi.fn().mockResolvedValueOnce([
                {
                  id: 'mockDoc1',
                  filename: 'doc1.jpeg',
                  documentKey: 'applications/mockAppId/doc1.jpeg',
                },
                {
                  id: 'mockDoc2',
                  filename: 'doc2.jpeg',
                  documentKey: 'applications/mockAppId/doc2.jpeg',
                },
              ]),
            } as unknown as DocumentRepository,
            feedbackRepository: {} as unknown as PredictionFeedbackRepository,
            messagingService: {} as unknown as MessagingService,
            partnerService: {
              hasFeature: vi.fn().mockResolvedValueOnce(false),
              findById: vi
                .fn()
                .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
              hasAnyProgramWithFeature: vi.fn().mockResolvedValueOnce(true),
            } as unknown as PartnerService,
            programService: {} as unknown as ProgramService,
            summaryRepository: {} as unknown as DocumentSummaryRepository,
            documentTagRepository: {} as unknown as DocumentTagRepository,
            documentFieldRepository: {} as unknown as DocumentFieldRepository,
            uploadRepository: {
              upload: mockUploadFn.mockResolvedValueOnce([
                { filename: 'doc1.jpeg', documentKey: 'applications/mockAppId/doc1.jpeg' },
                { filename: 'doc2.jpeg', documentKey: 'applications/mockAppId/doc2.jpeg' },
              ]),
            } as unknown as UploadRepository,
          });

          const resp = await mockService.upload(
            { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
            {
              relation: { type: 'profile', id: 'mockProfileId' },
              files: [
                Promise.resolve({
                  filename: 'doc1.heic',
                  mimetype: 'image/heic',
                  createReadStream: () => mockIterator,
                  encoding: '7bit',
                }),
                Promise.resolve({
                  filename: 'doc2.heif',
                  mimetype: 'image/heic',
                  createReadStream: () => mockIterator,
                  encoding: '7bit',
                }),
              ] as unknown as Promise<FileUpload>[],
              programId: 'mockProgramId',
            } as UploadDocumentsInput,
          );

          expect(mockUploadFn).toHaveBeenCalledWith({
            partner: { id: 'mockPartner', externalId: 'partner-external' },
            prefix: 'profiles/mockProfileId',
            files: [
              {
                filename: 'doc1.jpeg',
                mimetype: 'image/jpeg',
                createReadStream: expect.any(Function),
                encoding: '7bit',
              },
              {
                filename: 'doc2.jpeg',
                mimetype: 'image/jpeg',
                createReadStream: expect.any(Function),
                encoding: '7bit',
              },
            ],
          });
          expect(mockInsertFn).toHaveBeenCalledWith([
            {
              filename: 'doc1.jpeg',
              documentKey: 'applications/mockAppId/doc1.jpeg',
              uploaderId: 'mockUserId',
              sha256: expect.any(String),
            },
            {
              filename: 'doc2.jpeg',
              documentKey: 'applications/mockAppId/doc2.jpeg',
              uploaderId: 'mockUserId',
              sha256: expect.any(String),
            },
          ]);
          expect(resp).toEqual([
            {
              id: 'mockDoc1',
              filename: 'doc1.jpeg',
              documentKey: 'applications/mockAppId/doc1.jpeg',
            },
            {
              id: 'mockDoc2',
              filename: 'doc2.jpeg',
              documentKey: 'applications/mockAppId/doc2.jpeg',
            },
          ]);
        });
      });
      describe('when the conversion fails', () => {
        it('should upload and save the original image', async () => {
          const mockIterator = (async function* () {
            yield Buffer.from('pixels');
            yield Buffer.from('morepixels');
          })();
          const mockHeicConvert = heic as vi.mockedFunction<typeof heic>;
          mockHeicConvert
            .mockRejectedValueOnce(new Error('this totally sucks my dude'))
            .mockRejectedValueOnce(new Error('like, the worst, brother'));
          const mockPublish = vi.fn();
          const mockUploadFn = vi.fn();
          const mockInsertFn = vi.fn();
          const mockService = new DocumentService({
            documentRepository: {
              insert: mockInsertFn.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
              findBy: vi.fn().mockResolvedValueOnce([
                {
                  id: 'mockDoc1',
                  filename: 'doc1.heic',
                  documentKey: 'applications/mockAppId/doc1.heic',
                },
                {
                  id: 'mockDoc2',
                  filename: 'doc2.heif',
                  documentKey: 'applications/mockAppId/doc2.heif',
                },
              ]),
            } as unknown as DocumentRepository,
            feedbackRepository: {} as unknown as PredictionFeedbackRepository,
            messagingService: { publish: mockPublish } as unknown as MessagingService,
            partnerService: {
              hasFeature: vi.fn().mockResolvedValueOnce(false),
              findById: vi
                .fn()
                .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
              hasAnyProgramWithFeature: vi.fn().mockResolvedValueOnce(true),
            } as unknown as PartnerService,
            programService: {} as unknown as ProgramService,
            summaryRepository: {} as unknown as DocumentSummaryRepository,
            documentTagRepository: {} as unknown as DocumentTagRepository,
            documentFieldRepository: {} as unknown as DocumentFieldRepository,
            uploadRepository: {
              upload: mockUploadFn.mockResolvedValueOnce([
                { filename: 'doc1.heic', documentKey: 'applications/mockAppId/doc1.heic' },
                { filename: 'doc2.heif', documentKey: 'applications/mockAppId/doc2.heif' },
              ]),
            } as unknown as UploadRepository,
          });

          const resp = await mockService.upload(
            { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
            {
              relation: { type: 'profile', id: 'mockProfileId' },
              files: [
                Promise.resolve({
                  filename: 'doc1.heic',
                  mimetype: 'image/heic',
                  createReadStream: () => mockIterator,
                  encoding: '7bit',
                }),
                Promise.resolve({
                  filename: 'doc2.heif',
                  mimetype: 'image/heif',
                  createReadStream: () => mockIterator,
                  encoding: '7bit',
                }),
              ] as unknown as Promise<FileUpload>[],
              programId: 'mockProgramId',
            } as UploadDocumentsInput,
          );

          expect(mockUploadFn).toHaveBeenCalledWith({
            partner: { id: 'mockPartner', externalId: 'partner-external' },
            prefix: 'profiles/mockProfileId',
            files: [
              {
                filename: 'doc1.heic',
                mimetype: 'image/heic',
                createReadStream: expect.any(Function),
                encoding: '7bit',
              },
              {
                filename: 'doc2.heif',
                mimetype: 'image/heif',
                createReadStream: expect.any(Function),
                encoding: '7bit',
              },
            ],
          });
          expect(mockInsertFn).toHaveBeenCalledWith([
            {
              filename: 'doc1.heic',
              documentKey: 'applications/mockAppId/doc1.heic',
              uploaderId: 'mockUserId',
              sha256: expect.any(String),
            },
            {
              filename: 'doc2.heif',
              documentKey: 'applications/mockAppId/doc2.heif',
              uploaderId: 'mockUserId',
              sha256: expect.any(String),
            },
          ]);
          expect(resp).toEqual([
            {
              id: 'mockDoc1',
              filename: 'doc1.heic',
              documentKey: 'applications/mockAppId/doc1.heic',
            },
            {
              id: 'mockDoc2',
              filename: 'doc2.heif',
              documentKey: 'applications/mockAppId/doc2.heif',
            },
          ]);
          expect(mockLogger.warn).toHaveBeenCalledWith(
            { error: new Error('this totally sucks my dude') },
            'DocumentService.convertFiles: failure converting doc1.heic, leaving as heic',
          );
          expect(mockLogger.warn).toHaveBeenCalledWith(
            { error: new Error('like, the worst, brother') },
            'DocumentService.convertFiles: failure converting doc2.heif, leaving as heif',
          );
        });
      });
    });

    describe('when the upload fails', () => {
      it('does not save to the db', async () => {
        const insert = vi.fn();
        const upload = vi.fn();
        const mockService = new DocumentService({
          documentRepository: {
            insert: insert.mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
            findBy: vi.fn().mockResolvedValueOnce([
              {
                id: 'mockDoc1',
                filename: 'doc1.pdf',
                documentKey: 'applications/mockAppId/doc1.pdf',
              },
              {
                id: 'mockDoc2',
                filename: 'doc2.pdf',
                documentKey: 'applications/mockAppId/doc2.pdf',
              },
            ]),
            save: vi.fn().mockResolvedValueOnce({
              id: 'mockDocumentId',
              documentKey: 'mockDocumentKey',
              uploaderId: 'mockUploaderId',
              filename: 'mockFileName.jpg',
              mimetype: 'image/jpeg',
              createdAt: new Date('11/01/2022'),
            }),
          } as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {
            findById: vi
              .fn()
              .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
          } as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {
            upload: upload.mockRejectedValueOnce(new Error('Upload Error')),
          } as unknown as UploadRepository,
        });

        await expect(
          mockService.upload(
            { userId: 'mockUserId', partnerId: 'mockPartnerId' } as unknown as LoginToken,
            {
              relation: { type: 'application', id: 'mockAppId' },
              files: [
                Promise.resolve({ filename: 'doc1.pdf' }),
                Promise.resolve({ filename: 'doc2.pdf' }),
              ],
            } as UploadDocumentsInput,
          ),
        ).rejects.toThrow();

        expect(insert).not.toHaveBeenCalled();
      });
    });
  });

  describe('pin', () => {
    it('should call save to set new pinned value and return the updated document', async () => {
      const save = vi.fn();
      const mockService = new DocumentService({
        documentRepository: {
          insert: vi.fn().mockResolvedValueOnce({ identifiers: ['mockDoc1', 'mockDoc2'] }),
          findBy: vi.fn().mockResolvedValueOnce([
            {
              id: 'mockDoc1',
              filename: 'doc1.pdf',
              documentKey: 'applications/mockAppId/doc1.pdf',
            },
            {
              id: 'mockDoc2',
              filename: 'doc2.pdf',
              documentKey: 'applications/mockAppId/doc2.pdf',
            },
          ]),
          save: save.mockResolvedValueOnce({
            id: 'mockDocumentId',
            documentKey: 'mockDocumentKey',
            uploaderId: 'mockUploaderId',
            filename: 'mockFileName.jpg',
            mimetype: 'image/jpeg',
            createdAt: new Date('11/01/2022'),
          }),
        } as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {
          findById: vi
            .fn()
            .mockResolvedValueOnce({ id: 'mockPartner', externalId: 'partner-external' }),
        } as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {
          upload: vi.fn().mockResolvedValueOnce([
            { filename: 'doc1.pdf', documentKey: 'applications/mockAppId/doc1.pdf' },
            { filename: 'doc2.pdf', documentKey: 'applications/mockAppId/doc2.pdf' },
          ]),
        } as unknown as UploadRepository,
      });

      const result = await mockService.pin({ id: 'mockDocumentId', pinned: true });
      expect(save).toHaveBeenCalledTimes(1);
      expect(result).toEqual(
        expect.objectContaining({
          record: {
            id: 'mockDocumentId',
            documentKey: 'mockDocumentKey',
            uploaderId: 'mockUploaderId',
            filename: 'mockFileName.jpg',
            mimetype: 'image/jpeg',
            createdAt: new Date('11/01/2022'),
          },
        }),
      );
    });
  });

  describe('findByRelation', () => {
    it('groups multiple requests for the same relation into a single query', async () => {
      const mockFindByRelationIds = vi.fn().mockResolvedValueOnce([
        { id: 'mockProfile1', documentIds: ['mockDocument1', 'mockDocument2'] },
        { id: 'mockProfile2', documentIds: ['mockDocument3', 'mockDocument4'] },
      ]);
      const mockFindDocuments = vi
        .fn()
        .mockResolvedValueOnce([
          { id: 'mockDocument2' },
          { id: 'mockDocument3' },
          { id: 'mockDocument1' },
          { id: 'mockDocument4' },
        ]);
      const service = new DocumentService({
        documentRepository: {
          findByRelationIds: mockFindByRelationIds,
          findBy: mockFindDocuments,
        } as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });

      const [profile2Documents, profile1Documents] = await Promise.all([
        service.findByRelation({ id: 'mockProfile2', type: 'profile' }),
        service.findByRelation({ id: 'mockProfile1', type: 'profile' }),
      ]);

      expect(profile2Documents).toEqual([{ id: 'mockDocument3' }, { id: 'mockDocument4' }]);
      expect(profile1Documents).toEqual([{ id: 'mockDocument1' }, { id: 'mockDocument2' }]);
      expect(mockFindByRelationIds).toHaveBeenCalledTimes(1);
      expect(mockFindByRelationIds).toHaveBeenCalledWith(
        ['mockProfile2', 'mockProfile1'],
        'profile',
      );
      expect(mockFindDocuments).toHaveBeenCalledTimes(1);
      expect(mockFindDocuments).toHaveBeenCalledWith({
        id: In(['mockDocument3', 'mockDocument4', 'mockDocument1', 'mockDocument2']),
      });
    });

    it('groups multiple requests for different relations into a single query per relation', async () => {
      const mockFindByRelationIds = vi
        .fn()
        .mockResolvedValueOnce([
          { id: 'mockProfile1', documentIds: ['mockDocument1', 'mockDocument2'] },
          { id: 'mockProfile2', documentIds: ['mockDocument3', 'mockDocument4'] },
        ])
        .mockResolvedValueOnce([
          { id: 'mockVendor1', documentIds: ['mockDocument5', 'mockDocument7'] },
          { id: 'mockVendor2', documentIds: ['mockDocument6', 'mockDocument8'] },
        ]);
      const mockFindDocuments = vi
        .fn()
        .mockResolvedValueOnce([
          { id: 'mockDocument2' },
          { id: 'mockDocument3' },
          { id: 'mockDocument1' },
          { id: 'mockDocument4' },
          { id: 'mockDocument5' },
          { id: 'mockDocument6' },
          { id: 'mockDocument7' },
          { id: 'mockDocument8' },
        ]);
      const service = new DocumentService({
        documentRepository: {
          findByRelationIds: mockFindByRelationIds,
          findBy: mockFindDocuments,
        } as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });

      const [profile2Documents, vendor1Documents, profile1Documents, vendor2Documents] =
        await Promise.all([
          service.findByRelation({ id: 'mockProfile2', type: 'profile' }),
          service.findByRelation({ id: 'mockVendor1', type: 'vendor' }),
          service.findByRelation({ id: 'mockProfile1', type: 'profile' }),
          service.findByRelation({ id: 'mockVendor2', type: 'vendor' }),
        ]);

      expect(profile2Documents).toEqual([{ id: 'mockDocument3' }, { id: 'mockDocument4' }]);
      expect(profile1Documents).toEqual([{ id: 'mockDocument1' }, { id: 'mockDocument2' }]);
      expect(vendor1Documents).toEqual([{ id: 'mockDocument5' }, { id: 'mockDocument7' }]);
      expect(vendor2Documents).toEqual([{ id: 'mockDocument6' }, { id: 'mockDocument8' }]);
      expect(mockFindByRelationIds).toHaveBeenCalledTimes(2);
      expect(mockFindByRelationIds).toHaveBeenCalledWith(
        ['mockProfile2', 'mockProfile1'],
        'profile',
      );
      expect(mockFindByRelationIds).toHaveBeenCalledWith(['mockVendor1', 'mockVendor2'], 'vendor');
      expect(mockFindDocuments).toHaveBeenCalledTimes(1);
      expect(mockFindDocuments).toHaveBeenCalledWith({
        id: In([
          'mockDocument3',
          'mockDocument4',
          'mockDocument1',
          'mockDocument2',
          'mockDocument5',
          'mockDocument7',
          'mockDocument6',
          'mockDocument8',
        ]),
      });
    });

    it('returns an empty array if no documents are found', async () => {
      const mockFindByRelationIds = vi.fn().mockResolvedValueOnce([
        { id: 'mockProfile1', documentIds: ['mockDocument1', 'mockDocument2'] },
        { id: 'mockProfile2', documentIds: ['mockDocument3', 'mockDocument4'] },
      ]);
      const mockFindDocuments = vi.fn();

      const service = new DocumentService({
        documentRepository: {
          findByRelationIds: mockFindByRelationIds,
          findBy: mockFindDocuments,
        } as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });

      const profile3Documents = await service.findByRelation({
        id: 'mockProfile3',
        type: 'profile',
      });

      expect(profile3Documents).toEqual([]);
      expect(mockFindByRelationIds).toHaveBeenCalledTimes(1);
      expect(mockFindByRelationIds).toHaveBeenCalledWith(['mockProfile3'], 'profile');
      expect(mockFindDocuments).not.toHaveBeenCalled();
    });
  });

  describe('classify', () => {
    describe('if the request fails', () => {
      it('should log a warning, the response, and return', async () => {
        const mockPublish = vi.fn();
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {
            publishBulk: mockPublish.mockRejectedValueOnce(new Error('bad news my guy')),
          } as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const response = await service.classify([
          { id: 'mockDocumentId' },
        ] as unknown as Document[]);

        expect(response).toBeUndefined();
        expect(mockPublish).toHaveBeenCalledTimes(1);
        expect(mockPublish).toHaveBeenCalledWith({
          messages: [{ document_id: 'mockDocumentId' }],
          topicName: 'document-upload-[env]',
        });
        expect(mockLogger.warn).toHaveBeenCalledWith(
          { error: new Error('bad news my guy') },
          'DocumentService.classify: error publishing messages',
        );
      });
    });
    describe('if the request is successful', () => {
      it('should return', async () => {
        const mockPublish = vi.fn();
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {
            publishBulk: mockPublish.mockResolvedValueOnce(123456),
          } as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const response = await service.classify([
          { id: 'mockDocumentId' },
        ] as unknown as Document[]);

        expect(response).toBeUndefined();
        expect(mockPublish).toHaveBeenCalledTimes(1);
        expect(mockPublish).toHaveBeenCalledWith({
          messages: [{ document_id: 'mockDocumentId' }],
          topicName: 'document-upload-[env]',
        });
      });
      it('should include the program specific model id if it exists', async () => {
        const mockPublish = vi.fn();
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {
            publishBulk: mockPublish.mockResolvedValueOnce(123456),
          } as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {
            findById: vi
              .fn()
              .mockResolvedValueOnce({ config: { documents: { modelId: 'mockModelId' } } }),
          } as unknown as ProgramService,
          summaryRepository: {} as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const response = await service.classify(
          [{ id: 'mockDocumentId' }] as unknown as Document[],
          'mockProgramId',
        );

        expect(response).toBeUndefined();
        expect(mockPublish).toHaveBeenCalledTimes(1);
        expect(mockPublish).toHaveBeenCalledWith({
          messages: [{ document_id: 'mockDocumentId', model_id: 'mockModelId' }],
          topicName: 'document-upload-[env]',
        });
      });
    });
  });

  describe('getSummary', () => {
    it('should return the summary from the summaryRepo, using the dataloader', async () => {
      const findFn = vi
        .fn()
        .mockResolvedValueOnce([{ id: 'mockSummaryId', documentId: 'mockDocId' }]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: { findByDocumentIds: findFn } as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getSummary('mockDocId');
      expect(result).toEqual({ id: 'mockSummaryId', documentId: 'mockDocId' });
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
    it('should return undefined if nothing is found', async () => {
      const findFn = vi.fn().mockResolvedValueOnce([]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: { findByDocumentIds: findFn } as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getSummary('mockDocId');
      expect(result).toBeUndefined;
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
  });
  describe('getSummary', () => {
    it('should return the summary from the summaryRepo, using the dataloader', async () => {
      const findFn = vi
        .fn()
        .mockResolvedValueOnce([{ id: 'mockSummaryId', documentId: 'mockDocId' }]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: { findByDocumentIds: findFn } as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getSummary('mockDocId');
      expect(result).toEqual({ id: 'mockSummaryId', documentId: 'mockDocId' });
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
    it('should return undefined if nothing is found', async () => {
      const findFn = vi.fn().mockResolvedValueOnce([]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: { findByDocumentIds: findFn } as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getSummary('mockDocId');
      expect(result).toBeUndefined;
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
  });

  describe('getDocumentTags', () => {
    it('should return collection of documentTags from documentTagRepository, using the dataloader', async () => {
      const findFn = vi
        .fn()
        .mockResolvedValueOnce([{ id: 'mockDocumentTagId', documentId: 'mockDocId' }]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: {} as unknown as DocumentSummaryRepository,
        documentTagRepository: { findByDocumentIds: findFn } as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getDocumentTags('mockDocId');
      expect(result).toEqual([{ id: 'mockDocumentTagId', documentId: 'mockDocId' }]);
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
    it('should return unedfined if nothing is found', async () => {
      const findFn = vi.fn().mockResolvedValueOnce([]);
      const service = new DocumentService({
        documentRepository: {} as unknown as DocumentRepository,
        feedbackRepository: {} as unknown as PredictionFeedbackRepository,
        messagingService: {} as unknown as MessagingService,
        partnerService: {} as unknown as PartnerService,
        programService: {} as unknown as ProgramService,
        summaryRepository: { findByDocumentIds: findFn } as unknown as DocumentSummaryRepository,
        documentTagRepository: {} as unknown as DocumentTagRepository,
        documentFieldRepository: {} as unknown as DocumentFieldRepository,
        uploadRepository: {} as unknown as UploadRepository,
      });
      const result = await service.getSummary('mockDocId');
      expect(result).toBeUndefined;
      expect(findFn).toHaveBeenCalledWith(['mockDocId']);
    });
  });

  describe('submitPredictionFeedback', () => {
    describe('errors', () => {
      it('should throw if the query fails', async () => {
        const findSummaryFn = vi
          .fn()
          .mockRejectedValueOnce(new Error('THE FRIDGE IS EMPTY MY GUY'));
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.Yes,
        });
        expect(result).toEqual({
          metadata: {
            status: 500,
            message: 'Internal Server Error',
            errors: ['Internal Server Error'],
          },
          query: {},
        });
        expect(mockLogger.error).toHaveBeenCalledWith(
          { error: 'Error: THE FRIDGE IS EMPTY MY GUY' },
          'DocumentService.submitPredictionFeedback: unexpected error',
        );
      });
      it('should throw if there is no summary found', async () => {
        const findSummaryFn = vi.fn().mockResolvedValueOnce([]);
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.Yes,
        });
        expect(result).toEqual({
          metadata: {
            status: 500,
            message: 'Internal Server Error',
            errors: ['Internal Server Error'],
          },
          query: {},
        });
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            error:
              'Error: DocumentService.submitPredictionFeedback: no summary or prediction found for document mockDocId',
          },
          'DocumentService.submitPredictionFeedback: unexpected error',
        );
      });
      it('should throw if there is no prediction found', async () => {
        const findSummaryFn = vi
          .fn()
          .mockResolvedValueOnce([{ id: 'mockSummaryId', documentId: 'mockDocId' }]);
        const service = new DocumentService({
          documentRepository: {} as unknown as DocumentRepository,
          feedbackRepository: {} as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.Yes,
        });
        expect(result).toEqual({
          metadata: {
            status: 500,
            message: 'Internal Server Error',
            errors: ['Internal Server Error'],
          },
          query: {},
        });
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            error:
              'Error: DocumentService.submitPredictionFeedback: no summary or prediction found for document mockDocId',
          },
          'DocumentService.submitPredictionFeedback: unexpected error',
        );
      });
    });
    describe('successes', () => {
      it('should save the accuracy to the database and return the updated record', async () => {
        const findSummaryFn = vi.fn().mockResolvedValueOnce([
          {
            id: 'mockSummaryId',
            prediction: { id: 'mockPredictionId' },
            documentId: 'mockDocId',
          },
        ]);
        const saveFeedbackFn = vi.fn().mockResolvedValueOnce({ id: 'mockFeedbackId' });
        const service = new DocumentService({
          documentRepository: {
            findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockDocId' }]),
            findById: vi.fn().mockResolvedValueOnce({ id: 'mockDocId' }),
          } as unknown as DocumentRepository,
          feedbackRepository: {
            findOne: vi.fn().mockResolvedValueOnce(null),
            save: saveFeedbackFn,
          } as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.Yes,
        });
        expect(result.metadata.status).toEqual(200);
        expect(saveFeedbackFn).toHaveBeenCalledWith(
          expect.objectContaining({
            accurate: 'yes',
            adminId: 'mockAdminId',
            predictionId: 'mockPredictionId',
          }),
        );
      });
      it('should save the preferred label to the database and return the updated record', async () => {
        const findSummaryFn = vi.fn().mockResolvedValueOnce([
          {
            id: 'mockSummaryId',
            prediction: { id: 'mockPredictionId' },
            documentId: 'mockDocId',
          },
        ]);
        const saveFeedbackFn = vi.fn().mockResolvedValueOnce({ id: 'mockFeedbackId' });
        const service = new DocumentService({
          documentRepository: {
            findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockDocId' }]),
            findById: vi.fn().mockResolvedValueOnce({ id: 'mockDocId' }),
          } as unknown as DocumentRepository,
          feedbackRepository: {
            findOne: vi.fn().mockResolvedValueOnce(null),
            save: saveFeedbackFn,
          } as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.No,
          preferredLabelId: 'mockPreferredLabelId',
        });
        expect(result.metadata.status).toEqual(200);
        expect(saveFeedbackFn).toHaveBeenCalledWith(
          expect.objectContaining({
            accurate: 'no',
            adminId: 'mockAdminId',
            predictionId: 'mockPredictionId',
            preferredLabelId: 'mockPreferredLabelId',
          }),
        );
      });
      it('should deactivate old feedback by the submitting admin if it exists', async () => {
        const findSummaryFn = vi.fn().mockResolvedValueOnce([
          {
            id: 'mockSummaryId',
            prediction: { id: 'mockPredictionId' },
            documentId: 'mockDocId',
          },
        ]);
        const deleteFeedbackFn = vi.fn().mockResolvedValueOnce({});
        const saveFeedbackFn = vi.fn().mockResolvedValueOnce({ id: 'mockFeedbackId' });
        const service = new DocumentService({
          documentRepository: {
            findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockDocId' }]),
            findById: vi.fn().mockResolvedValueOnce({ id: 'mockDocId' }),
          } as unknown as DocumentRepository,
          feedbackRepository: {
            findOne: vi.fn().mockResolvedValueOnce({ id: 'mockExistingFeedbackId' }),
            softDelete: deleteFeedbackFn,
            save: saveFeedbackFn,
          } as unknown as PredictionFeedbackRepository,
          messagingService: {} as unknown as MessagingService,
          partnerService: {} as unknown as PartnerService,
          programService: {} as unknown as ProgramService,
          summaryRepository: {
            findByDocumentIds: findSummaryFn,
          } as unknown as DocumentSummaryRepository,
          documentTagRepository: {} as unknown as DocumentTagRepository,
          documentFieldRepository: {} as unknown as DocumentFieldRepository,
          uploadRepository: {} as unknown as UploadRepository,
        });
        const result = await service.submitPredictionFeedback(mockAdminToken, {
          id: 'mockDocId',
          accurate: YesNoUnsure.No,
          preferredLabelId: 'mockPreferredLabelId',
        });
        expect(result.metadata.status).toEqual(200);
        expect(deleteFeedbackFn).toHaveBeenCalledWith({ id: 'mockExistingFeedbackId' });
        expect(saveFeedbackFn).toHaveBeenCalledWith(
          expect.objectContaining({
            accurate: 'no',
            adminId: 'mockAdminId',
            predictionId: 'mockPredictionId',
            preferredLabelId: 'mockPreferredLabelId',
          }),
        );
      });
    });
  });
});

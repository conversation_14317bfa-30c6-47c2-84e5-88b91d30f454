import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { IResolvers } from '@graphql-tools/utils';
import { AdminContext, Context } from '@platform-api/@types/graphql.js';
import { GraphQLFieldResolver } from 'graphql';
import { PresetGuestTokenInput } from '../../../services/preset/PresetService.js';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';

type PresetMutations = {
  generatePresetGuestToken: GraphQLFieldResolver<
    void,
    AdminContext,
    { input: PresetGuestTokenInput }
  >;
};

type PresetResolvers = IResolvers<void, Context> & {
  Mutation: {
    preset: () => { _type: 'PresetMutations' };
  };
  PresetMutations: PresetMutations;
};

const generatePresetGuestToken: GraphQLFieldResolver<
  void,
  AdminContext,
  { input: PresetGuestTokenInput }
> = async (_, { input }, context) => {
  return await context.services.preset.generateGuestToken({
    ...input,
    userId: context.token.userId,
  });
};

const presetResolvers: PresetResolvers = {
  Mutation: {
    preset: () => ({ _type: 'PresetMutations' }),
  },
  PresetMutations: {
    generatePresetGuestToken,
  },
};

const resolverComposition = {
  'PresetMutations.generatePresetGuestToken': [IsAdmin()],
};

export default composeResolvers(presetResolvers, resolverComposition);

import { describe, expect, it, vi, beforeEach } from 'vitest';
import { GraphQLResolveInfo } from 'graphql';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { PresetGuestTokenInput } from '../../../services/preset/PresetService.js';
import presetResolvers from './preset.resolver.js';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { mockLoginToken } from '@platform-api-test/mocks.js';

describe('Preset Resolvers', () => {
  let mockContext: AdminContext;
  let mockPresetService: {
    generateGuestToken: ReturnType<typeof vi.fn>;
    refreshGuestToken: ReturnType<typeof vi.fn>;
  };

  beforeEach(() => {
    mockPresetService = {
      generateGuestToken: vi.fn(),
      refreshGuestToken: vi.fn(),
    };

    mockContext = {
      token: {
        userId: 'test-user-id',
        partnerId: 'test-partner-id',
        adminId: 'mockAdminId',
        identityUserId: 'test-identity-user-id',
      },
      services: {
        admins: {
          findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
        },
        preset: mockPresetService,
        identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
      },
    } as unknown as AdminContext;
  });

  describe('Mutation.preset', () => {
    it('should return PresetMutations parent object', () => {
      const result = presetResolvers.Mutation.preset();
      expect(result).toEqual({ _type: 'PresetMutations' });
    });
  });

  describe('PresetMutations.generatePresetGuestToken', () => {
    const mockInput: PresetGuestTokenInput = {
      dashboardId: 'test-dashboard-id',
      userId: 'input-user-id',
    };

    const mockResponse = {
      token: 'mock-guest-token',
      expiresAt: '2024-01-01T00:00:00.000Z',
    };

    it('should call generateGuestToken with correct parameters', async () => {
      mockPresetService.generateGuestToken.mockResolvedValueOnce(mockResponse);

      const result = await presetResolvers.PresetMutations.generatePresetGuestToken(
        undefined,
        { input: mockInput },
        mockContext,
        {} as GraphQLResolveInfo,
      );

      expect(mockPresetService.generateGuestToken).toHaveBeenCalledWith({
        dashboardId: 'test-dashboard-id',
        userId: 'test-user-id',
      });
      expect(result).toEqual(mockResponse);
    });

    it('should override input userId with context token userId', async () => {
      mockPresetService.generateGuestToken.mockResolvedValueOnce(mockResponse);

      await presetResolvers.PresetMutations.generatePresetGuestToken(
        undefined,
        { input: mockInput },
        mockContext,
        {} as GraphQLResolveInfo,
      );

      const calledWith = mockPresetService.generateGuestToken.mock.calls[0][0];
      expect(calledWith.userId).toBe('test-user-id');
      expect(calledWith.userId).not.toBe('input-user-id');
    });

    it('should handle input without rlsClauses', async () => {
      const inputWithoutRls = {
        dashboardId: 'test-dashboard-id',
        userId: 'input-user-id',
      };
      mockPresetService.generateGuestToken.mockResolvedValueOnce(mockResponse);

      await presetResolvers.PresetMutations.generatePresetGuestToken(
        undefined,
        { input: inputWithoutRls },
        mockContext,
        {} as GraphQLResolveInfo,
      );

      expect(mockPresetService.generateGuestToken).toHaveBeenCalledWith({
        dashboardId: 'test-dashboard-id',
        userId: 'test-user-id',
      });
    });

    it('should propagate errors from preset service', async () => {
      const serviceError = new Error('Preset service error');
      mockPresetService.generateGuestToken.mockRejectedValueOnce(serviceError);

      await expect(
        presetResolvers.PresetMutations.generatePresetGuestToken(
          undefined,
          { input: mockInput },
          mockContext,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow('Preset service error');
    });

    it('throws if user is a non-admin', async () => {
      await expect(
        presetResolvers.PresetMutations.generatePresetGuestToken(
          undefined,
          { input: mockInput },
          { ...mockContext, token: mockLoginToken } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow(AuthorizationError);
    });
  });
});

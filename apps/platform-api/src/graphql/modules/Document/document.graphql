type OCRResult {
  id: UUID!
  modelVersion: ModelVersion!
  raw: JSON
}

type ModelVersion {
  id: UUID!
  name: NonEmptyString!
  type: NonEmptyString!
  description: NonEmptyString
  createdAt: DateTime!
}

enum YesNoUnsure {
  yes
  no
  unsure
}

type Feedback {
  id: UUID!
  accurate: YesNoUnsure!
  preferredLabel: ActiveLabel
  admin: Admin!
}

type Prediction {
  id: UUID!
  label: ActiveLabel!
  probability: Float!
  feedback: [Feedback!]!
  createdAt: DateTime!
}

type ActiveLabel {
  id: UUID!
  name: NonEmptyString!
  displayName: NonEmptyString
  modelVersion: ModelVersion
  createdAt: DateTime!
}

type DoctopusTag {
  id: UUID!
  name: String!
  description: String
  type: String!
  visibility: String!
  createdAt: DateTime!
  deactivatedAt: DateTime
}

type DocumentTag {
  id: UUID!
  documentId: UUID!
  tagId: UUID!
  tag: DoctopusTag!
  createdAt: DateTime!
  deactivatedAt: DateTime
}

type DocumentField {
  id: UUID!
  docId: UUID!
  fieldKey: String!
  fieldValue: String!
  confidence: Float
  startIndex: Int
  stopIndex: Int
  valid: Boolean
  normalizedValue: String
  createdAt: DateTime!
  deactivatedAt: DateTime
}

type DocumentSummary {
  id: UUID!
  modelVersion: ModelVersion!
  prediction: Prediction
  ocr: OCRResult
  createdAt: DateTime!
}

type Document {
  id: UUID!
  createdAt: DateTime!
  documentKey: NonEmptyString!
  uploaderId: UUID!
  uploader: User
  filename: NonEmptyString!
  mimetype: NonEmptyString!
  previewUrl: URL
  pinned: Boolean
  documentTags: [DocumentTag!]!
  documentFields: [DocumentField!]!
  summary: DocumentSummary
  sha256: String
}

input UploadDocumentsInput {
  id: UUID!
  files: [Upload!]!
}

input RemoveDocumentsInput {
  id: UUID!
  documentIds: [UUID!]!
}

input PinDocumentInput {
  id: UUID!
  pinned: Boolean!
}

input SubmitPredictionFeedbackInput {
  id: UUID!
  accurate: YesNoUnsure!
  preferredLabelId: UUID
}

type DocumentsMutationResponse {
  metadata: ResponseMetadata!
  query: Query!
  record: Document
}

type DocumentMutations {
  pinDocument(input: PinDocumentInput!): DocumentsMutationResponse!
  submitFeedback(
    input: SubmitPredictionFeedbackInput!
  ): DocumentsMutationResponse!
}

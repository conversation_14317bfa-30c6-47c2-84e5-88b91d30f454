type CommunicationPreferences {
  email: Boolean
  sms: Boolean
}

input CommunicationPreferencesInput {
  email: Boolean
  sms: Boolean
}

type User {
  id: UUID!
  displayId: String!
  legacyId: String
  partnerId: UUID!
  partner: Partner!
  email: EmailAddress
  name: NonEmptyString!
  phone: PhoneNumber
  validatedEmail: Boolean!
  createdAt: DateTime!
  admin: Admin
  enrollments: [Enrollment!]!
  applications: [Application!]!
  newEmail: EmailAddress @deprecated(reason: "Deprecated feature, need to implement in GCIP")
  taxId: NonEmptyString
  applicantProfile: ApplicantProfile
  aggregatePayments: AggregatePayment!
  taxForms: [TaxForm!]!
  referrals: [ProgramReferral!]!
  documents: [Document!]!
  communicationPreferences: CommunicationPreferences
  savedViews: [SavedView!]
}

enum LegacyUserType {
  UnlinkedLegacyUser
  LinkedLegacyUser
  NonLegacyUser
}

input UserFilter {
  id: UUID
  profileId: UUID
  legacyUserType: LegacyUserType
  search: String
}

enum UserSortColumn {
  Name
  TotalPaid
}

input UserSort {
  column: UserSortColumn!
  direction: SortDirection!
}

type UserPage {
  users: [User!]!
  pageInfo: PageInfo!
}

input CreateUserInput {
  partnerId: UUID!
  email: EmailAddress
  name: NonEmptyString!
  phone: PhoneNumber
  applicantTypeId: UUID!
}

input LinkLegacyUserInput {
  id: UUID!
  legacyUserId: UUID!
}

input UpdateUserInput {
  id: UUID!
  secondaryEmail: EmailAddress
  email: EmailAddress
  name: NonEmptyString!
  phone: PhoneNumber
  taxId: NonEmptyString
  bankAccount: BankAccountInput
}

input UpdateUserProfileInput {
  secondaryEmail: EmailAddress
  email: EmailAddress
  name: NonEmptyString!
  phone: PhoneNumber
  taxId: NonEmptyString
  bankAccount: BankAccountInput
  applicantTypeId: UUID
  mailingAddress: CreateAddressInput
  communicationPreferences: CommunicationPreferencesInput
}

input SaveUserBankAccountInput {
  id: UUID!
  bankAccount: BankAccountInput!
}

type BulkUserMutationResponse {
  metadata: BulkResponseMetadata!
  query: Query!
  records: [User!]
}

type UserResponse {
  metadata: ResponseMetadata!
  record: User
}

type UserMutations {
  create(input: CreateUserInput!): UserResponse!
  createW9TaxForm(input: CreateW9TaxFormInput!): TaxFormMutationResponse!
  linkLegacyUser(input: LinkLegacyUserInput!): UserResponse!
  saveBankAccount(input: SaveUserBankAccountInput!): UserResponse!
  update(input: UpdateUserInput!): UserResponse!
  updateProfile(input: UpdateUserProfileInput!): UserResponse!
}

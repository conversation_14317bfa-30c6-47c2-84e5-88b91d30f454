import {
  Index,
  IndexToDocument,
  IndexToFilters,
  IndexToSortColumn,
  SearchArguments,
  SortDirection,
} from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { logger } from '../../../utils/logger.js';
import { ElasticsearchClient } from './client/ElasticsearchClient.js';
import { getFilterBuilder } from './filters/index.js';
import { getSearchBuilder } from './search/index.js';
import { getSortBuilder } from './sorts/index.js';
import { SearchResultTransformer } from './transformer/SearchResultTransformer.js';
import { PaginationContext, SearchOptions, SearchRepository, SearchResult } from './types.js';
import { CursorData, extractCursor, isValidCursor } from './utils/cursor.js';

// Repository-level configuration (not index-specific)
const DEFAULT_CONFIG = {
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 1000,
  },
  pit: {
    keepAlive: '1m',
    enabled: false, // Disabled per existing comment in analyzePaginationContext
  },
} as const;

export interface ElasticsearchRepositoryConfig {
  pagination?: {
    defaultPageSize?: number;
    maxPageSize?: number;
  };
  pit?: {
    keepAlive?: string;
    enabled?: boolean;
  };
}

/**
 * Production-ready Elasticsearch search repository with mandatory tenant isolation.
 *
 * Provides secure, configurable search across multiple indices with cursor-based pagination.
 * Delegates index-specific logic to specialized builders (Filter/Search/Sort).
 *
 * @see README.md for architecture overview and design decisions
 * @see api.md for detailed usage examples and configuration options
 */
export class ElasticsearchRepository implements SearchRepository {
  private readonly client: ElasticsearchClient;
  private readonly transformer: SearchResultTransformer;
  private readonly config: {
    pagination: { defaultPageSize: number; maxPageSize: number };
    pit: { keepAlive: string; enabled: boolean };
  };

  /**
   * Creates a new ElasticsearchRepository with optional configuration.
   *
   * @param client - Elasticsearch client for executing queries
   * @param transformer - Transforms raw Elasticsearch responses to typed results
   * @param config - Optional configuration for pagination and PIT settings
   */
  constructor({
    client,
    transformer,
    config = {},
  }: {
    client: ElasticsearchClient;
    transformer: SearchResultTransformer;
    config?: ElasticsearchRepositoryConfig;
  }) {
    this.client = client;
    this.transformer = transformer;
    this.config = {
      pagination: {
        defaultPageSize:
          config.pagination?.defaultPageSize ?? DEFAULT_CONFIG.pagination.defaultPageSize,
        maxPageSize: config.pagination?.maxPageSize ?? DEFAULT_CONFIG.pagination.maxPageSize,
      },
      pit: {
        keepAlive: config.pit?.keepAlive ?? DEFAULT_CONFIG.pit.keepAlive,
        enabled: config.pit?.enabled ?? DEFAULT_CONFIG.pit.enabled,
      },
    };
  }

  /**
   * Checks if the Elasticsearch cluster is healthy and responsive.
   *
   * @returns Promise resolving to true if cluster is healthy, false otherwise
   */
  public async healthCheck(): Promise<boolean> {
    try {
      return await this.client.healthCheck();
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.healthCheck: unexpected error`);
      return false;
    }
  }

  /**
   * Builds the Elasticsearch query with mandatory security filtering.
   *
   * Orchestrates multiple builders to construct a secure query:
   * 1. Security filters (partner isolation) - REQUIRED
   * 2. User filters (status, tags, etc.) - Optional
   * 3. Search terms (text search) - Optional
   *
   * @param token - Admin token for security context
   * @param args - Search arguments with filters and search terms
   * @returns Elasticsearch bool query with proper security isolation
   */
  private queryBuilder<I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
  ): estypes.QueryDslQueryContainer {
    const queryContext = this.createQueryContext();

    this.addSecurityFilters(queryContext, token, args.input.index);
    this.applyUserFilters<I>(queryContext, args);
    this.applySearchTerms<I>(queryContext, args);

    const query = queryContext.build();
    logger.debug({ token, args, query }, `${this.constructor.name}.queryBuilder: query`);
    return query;
  }

  private createQueryContext() {
    return {
      filter: [] as estypes.QueryDslQueryContainer[],
      must: [] as estypes.QueryDslQueryContainer[],
      mustNot: [] as estypes.QueryDslQueryContainer[],
      build: function () {
        return { bool: { must: this.must, filter: this.filter, must_not: this.mustNot } };
      },
    };
  }

  private addSecurityFilters<I extends Index>(
    queryContext: ReturnType<typeof this.createQueryContext>,
    token: AdminToken,
    index: I,
  ): void {
    if (!token.partnerId) {
      throw new Error('Partner ID is required for security filtering');
    }

    try {
      const filterBuilder = getFilterBuilder(index);
      // Enforce that every index MUST implement partner isolation
      filterBuilder.applyPartnerFilter(queryContext.filter, token.partnerId);
    } catch (err) {
      // Security is non-negotiable - fail if no implementation
      throw new Error(`Security filter implementation required for index: ${index}. ${err}`);
    }
  }

  private applyUserFilters<I extends Index>(
    queryContext: ReturnType<typeof this.createQueryContext>,
    args: SearchArguments<I>,
  ): void {
    const { filters } = args?.input ?? {};
    this.applyFilters<I>(args.input.index, queryContext.filter, queryContext.mustNot, filters);
  }

  private applySearchTerms<I extends Index>(
    queryContext: ReturnType<typeof this.createQueryContext>,
    args: SearchArguments<I>,
  ): void {
    this.applySearch<I>(args.input.index, queryContext.must, args);
  }

  private applyFilters<I extends Index>(
    index: I,
    filter: estypes.QueryDslQueryContainer[],
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: IndexToFilters[I],
  ): void {
    let filterBuilder: ReturnType<typeof getFilterBuilder>;
    try {
      filterBuilder = getFilterBuilder(index);
    } catch (err) {
      logger.warn({ index, err }, `No filter implementation for index: ${index}`);
      return;
    }

    filterBuilder.applyFilters(filter, mustNot, filters);
  }

  private applySearch<I extends Index>(
    index: I,
    must: estypes.QueryDslQueryContainer[],
    args: SearchArguments<I>,
  ): void {
    if (!args.input.search) return;

    const searchBuilder = getSearchBuilder(index);
    searchBuilder.applySearch(must, args);
  }

  private sortBuilder<I extends Index>(
    args: SearchArguments<I>,
    reverse = false,
  ): estypes.SortCombinations[] {
    const sort: estypes.SortCombinations[] = [];

    if (args?.sort) {
      const { column, direction } = args.sort;
      let order: 'asc' | 'desc' = direction === SortDirection.Ascending ? 'asc' : 'desc';

      // Reverse sort order for backward pagination
      if (reverse) {
        order = order === 'asc' ? 'desc' : 'asc';
      }

      const index = args.input.index;
      const sortBuilder = getSortBuilder(index);
      sortBuilder.applySort(sort, column as IndexToSortColumn[typeof index], order);
    }

    try {
      const sortBuilder = getSortBuilder(args.input.index);
      sortBuilder.applyDefaultSort(sort, reverse);
    } catch (error) {
      logger.warn(
        { index: args.input.index, error },
        `No default sort implementation for index: ${args.input.index}`,
      );
    }

    logger.debug({ args, sort, reverse }, `${this.constructor.name}.sortBuilder: sort`);
    return sort;
  }

  private validate<I extends Index>(token: AdminToken, args: SearchArguments<I>) {
    if (!token?.partnerId) throw new Error('no partnerId on token');
    if (!args?.input?.index) throw new Error('no index provided in args');
    return;
  }

  /**
   * Executes a search with mandatory tenant isolation and configurable pagination.
   *
   * All searches are automatically filtered by partner ID for security. Index-specific
   * logic is delegated to specialized builders for maintainability and extensibility.
   *
   * @param token - Admin token containing partnerId for tenant filtering
   * @param args - Search arguments including index, filters, search terms, and pagination
   * @returns Promise resolving to search results with cursor-based pagination
   *
   * @example Basic search
   * ```typescript
   * const results = await repo.search(token, {
   *   input: { index: 'cases_applications', search: 'john doe' }
   * });
   * ```
   *
   * @example With filters and pagination
   * ```typescript
   * const results = await repo.search(token, {
   *   input: {
   *     index: 'cases_applications',
   *     search: 'emergency',
   *     filters: { caseStatus: ['pending'] }
   *   },
   *   pagination: { take: 25, after: cursor }
   * });
   * ```
   *
   * @throws {Error} When partner ID is missing or security filters can't be applied
   * @throws {Error} When pagination parameters are invalid
   */
  public async search<I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
  ): Promise<SearchResult<I>> {
    try {
      const clientAvailable = await this.ensureClientAvailable();
      if (!clientAvailable) {
        logger.warn(`${this.constructor.name}.search: Elasticsearch client not available`);
        return this.transformer.createEmptyResult<I>();
      }

      this.validate<I>(token, args);
      return await this.executeSearchPipeline(token, args);
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.search: unexpected error`);
      return this.transformer.createEmptyResult<I>();
    }
  }

  private async ensureClientAvailable(): Promise<boolean> {
    const client = await this.client.getClient();
    if (!client) {
      logger.warn(`${this.constructor.name}.search: Elasticsearch client not available`);
      return false;
    }
    return true;
  }

  private async executeSearchPipeline<I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
  ): Promise<SearchResult<I>> {
    const context = this.analyzePaginationContext(args);
    const searchOptions = await this.buildSearchOptions(token, args, context);
    const result = await this.executeSearch<I>(searchOptions, context);
    return this.transformer.transform<I>(result, context);
  }

  /**
   * Analyzes and validates pagination parameters, extracting cursor data.
   *
   * Supports cursor-based pagination with optional Point-in-Time (PIT) contexts
   * for consistent results across large datasets.
   *
   * @param args - Search arguments containing pagination parameters
   * @returns Validated pagination context with decoded cursor data
   * @throws {Error} When both after and before cursors are specified
   * @throws {Error} When page size exceeds configured maximum
   */
  private analyzePaginationContext<I extends Index>(args: SearchArguments<I>): PaginationContext {
    const { pagination } = args;
    this.validatePaginationInput(pagination);

    const limit = pagination?.take || this.config.pagination.defaultPageSize;
    const direction = this.determinePaginationDirection(pagination);
    const cursor = this.extractPaginationCursor(direction, pagination);
    const decodedCursor = cursor ? this.decodeCursor(cursor) : undefined;

    return {
      direction,
      cursor,
      limit,
      pitId: decodedCursor?.pitId,
      keepAlive: decodedCursor?.pitId ? this.config.pit.keepAlive : '',
      // For now, leaving all PIT-related logic disabled. Result-skipping based on inconsistent
      // data during pagination exists as a problem now in our existing postgres-based
      // implementation, so it is not critical to resolve now. In the future, it should be
      // straightforward to re-enable this logic. Since it is encapsulated in the cursor,
      // clients will not be impacted.
      createPIT: this.config.pit.enabled,
    };
  }

  private validatePaginationInput(pagination?: {
    after?: string;
    before?: string;
    take?: number;
  }): void {
    if (pagination?.after && pagination?.before) {
      throw new Error('Cannot specify both after and before cursors');
    }

    if (
      pagination?.take &&
      (pagination.take < 1 || pagination.take > this.config.pagination.maxPageSize)
    ) {
      throw new Error(`Page size must be between 1 and ${this.config.pagination.maxPageSize}`);
    }
  }

  private determinePaginationDirection(pagination?: { after?: string; before?: string }):
    | 'forward'
    | 'backward' {
    return pagination?.before ? 'backward' : 'forward';
  }

  private extractPaginationCursor(
    direction: 'forward' | 'backward',
    pagination?: { after?: string; before?: string },
  ): string | undefined {
    return direction === 'backward' ? pagination?.before : pagination?.after;
  }

  private decodeCursor(cursor: string): CursorData {
    if (!isValidCursor(cursor)) {
      throw new Error('Invalid cursor format');
    }
    return extractCursor(cursor);
  }

  private async buildSearchOptions<I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
    context: PaginationContext,
  ): Promise<SearchOptions & { pitId?: string }> {
    const baseOptions = this.createBaseSearchOptions(token, args, context);
    const searchAfter = this.extractSearchAfterFromCursor(context.cursor);
    const searchOptions = await this.configureSearchWithPIT(baseOptions, args, context);

    return {
      ...searchOptions,
      ...(searchAfter && { search_after: searchAfter }),
    };
  }

  private createBaseSearchOptions<I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
    context: PaginationContext,
  ): SearchOptions {
    const isBackward = context.direction === 'backward';

    return {
      size: context.limit + 1, // n+1 pagination to determine if there is another page
      query: this.queryBuilder(token, args),
      sort: this.sortBuilder(args, isBackward),
      _source: args?.includeData ?? false,
    };
  }

  private extractSearchAfterFromCursor(cursor?: string): unknown[] | undefined {
    if (!cursor) return undefined;

    try {
      const cursorData = extractCursor(cursor);
      return cursorData.sort;
    } catch (error) {
      logger.warn({ error }, 'Failed to extract cursor');
      return undefined;
    }
  }

  private async configureSearchWithPIT<I extends Index>(
    baseOptions: SearchOptions,
    args: SearchArguments<I>,
    context: PaginationContext,
  ): Promise<SearchOptions> {
    if (context.pitId) {
      return this.configureExistingPIT(baseOptions, context);
    }

    if (context.createPIT) {
      return await this.configureNewPIT(baseOptions, args);
    }

    return this.configureIndexSearch(baseOptions, args);
  }

  private configureExistingPIT(
    baseOptions: SearchOptions,
    context: PaginationContext,
  ): SearchOptions {
    if (!context.pitId) {
      throw new Error('Expected pitId to be present when configuring existing PIT');
    }

    return {
      ...baseOptions,
      pit: {
        id: context.pitId,
        keep_alive: context.keepAlive,
      },
    };
  }

  private async configureNewPIT<I extends Index>(
    baseOptions: SearchOptions,
    args: SearchArguments<I>,
  ): Promise<SearchOptions> {
    try {
      const client = await this.client.getClient();
      const pitResponse = await client.openPointInTime({
        index: args.input.index,
        keep_alive: this.config.pit.keepAlive,
      });

      logger.debug({ pitId: pitResponse.id }, 'Created new PIT context');

      return {
        ...baseOptions,
        pit: {
          id: pitResponse.id,
          keep_alive: this.config.pit.keepAlive,
        },
      };
    } catch (error) {
      logger.error({ error }, 'Failed to create PIT, falling back to index search');
      return this.configureIndexSearch(baseOptions, args);
    }
  }

  private configureIndexSearch<I extends Index>(
    baseOptions: SearchOptions,
    args: SearchArguments<I>,
  ): SearchOptions {
    return {
      ...baseOptions,
      index: args.input.index,
    };
  }

  private async executeSearch<I extends Index>(
    searchOptions: SearchOptions & { pitId?: string },
    context: PaginationContext,
  ): Promise<estypes.SearchResponse<IndexToDocument[I]>> {
    const client = await this.client.getClient();
    const { pitId, ...esOptions } = searchOptions;

    try {
      logger.debug(
        { searchOptions: esOptions, context },
        `${this.constructor.name}.executeCursorSearch: executing search`,
      );

      const result = await client.search<IndexToDocument[I]>(esOptions);

      logger.debug(
        { resultCount: result.hits.hits.length, context },
        `${this.constructor.name}.executeCursorSearch: search completed`,
      );

      return result;
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.executeCursorSearch: unexpected error`);
      throw error;
    }
  }
}

import { Index, IndexToFilters } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';

/**
 * Abstract base class for building Elasticsearch filters for specific indices.
 * Implements the Strategy pattern to handle different filtering logic per index type.
 */
export abstract class FilterBuilder<I extends Index> {
  /**
   * Applies partner-based security filtering to ensure tenant isolation.
   * This method MUST be implemented by all filter builders to enforce security.
   *
   * @param filter - Array to accumulate security filter conditions
   * @param partnerId - Partner ID for tenant isolation
   */
  abstract applyPartnerFilter(filter: estypes.QueryDslQueryContainer[], partnerId: string): void;

  /**
   * Applies user-provided filters to the provided filter and mustNot arrays based on the index-specific logic.
   *
   * @param filter - Array to accumulate positive filter conditions
   * @param mustNot - Array to accumulate negative filter conditions (exclusions)
   * @param filters - Index-specific filter configuration
   */
  abstract applyFilters(
    filter: estypes.QueryDslQueryContainer[],
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: IndexToFilters[I],
  ): void;

  /**
   * Returns the index type this filter builder handles.
   */
  abstract getIndex(): I;
}

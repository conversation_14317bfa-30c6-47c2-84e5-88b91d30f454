import { VerificationThreshold } from '@bybeam/platform-lib/verification/threshold';
import {
  CaseParticipantStatus,
  CasesApplicationsFilters,
  IndexToFilters,
  NoAssignee,
  PaymentStatus,
  Priority,
} from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { FilterBuilder } from './FilterBuilder.js';

/**
 * Filter builder for the cases_applications index.
 * Handles all filtering logic specific to case and application data.
 */
export class CasesApplicationsFilterBuilder extends FilterBuilder<'cases_applications'> {
  getIndex(): 'cases_applications' {
    return 'cases_applications';
  }

  applyPartnerFilter(filter: estypes.QueryDslQueryContainer[], partnerId: string): void {
    filter.push({ match: { case_partner_id: partnerId } });
  }

  applyFilters(
    filter: estypes.QueryDslQueryContainer[],
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: IndexToFilters['cases_applications'],
  ): void {
    this.applyArchivedFilter(mustNot, filters);
    this.applyProgramIdFilter(filter, filters);
    this.applyCaseStatusFilter(filter, filters);
    this.applyPriorityFilters(filter, mustNot, filters);
    this.applyReferralFilters(filter, mustNot, filters);
    this.applyVerificationFilters(filter, filters);
    this.applyPaymentStatusFilters(filter, filters);
    this.applyTagFilters(filter, filters);
    this.applyAnswerFilters(filter, filters);
    this.applyAnswerReviewFilters(filter, filters);
    this.applyDocumentTagFilters(filter, filters);
    this.applyAssigneeFilters(filter, filters);
    this.applyApplicantTypeFilters(filter, filters);
    this.applyCaseParticipantStatusFilters(filter, filters);
  }

  /**
   * Filters out archived cases unless explicitly requested.
   * By default, excludes cases with a deactivation date.
   */
  private applyArchivedFilter(
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.archived) return;
    mustNot.push({ exists: { field: 'case_deactivated_at' } });
  }

  /**
   * Filters cases by specific program ID.
   */
  private applyProgramIdFilter(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.programId) {
      filter.push({ match: { case_program_id: filters.programId } });
    }
  }

  /**
   * Filters cases by status using keyword matching for exact matches.
   */
  private applyCaseStatusFilter(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.caseStatus) {
      filter.push({ terms: { 'case_status.keyword': filters.caseStatus } });
    }
  }

  /**
   * Applies complex priority filtering logic based on expedited and new applicant flags.
   * Handles combinations of expedited and new applicant priority states.
   */
  private applyPriorityFilters(
    filter: estypes.QueryDslQueryContainer[],
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.expedited === undefined && filters?.newApplicant === undefined) {
      return;
    }

    const { expedited, newApplicant } = filters;
    const include: Priority[] = [];
    const exclude: Priority[] = [];

    switch (true) {
      case expedited === true && newApplicant === true:
        include.push(Priority.ExpeditedNewApplicant);
        break;
      case expedited === false && newApplicant === false:
        exclude.push(Priority.ExpeditedNewApplicant, Priority.Expedited, Priority.NewApplicant);
        break;
      case expedited === true && newApplicant === false:
        include.push(Priority.Expedited);
        exclude.push(Priority.NewApplicant, Priority.ExpeditedNewApplicant);
        break;
      case expedited === false && newApplicant === true:
        include.push(Priority.NewApplicant);
        exclude.push(Priority.Expedited, Priority.ExpeditedNewApplicant);
        break;
      case expedited === undefined && newApplicant === true:
        include.push(Priority.NewApplicant, Priority.ExpeditedNewApplicant);
        break;
      case expedited === undefined && newApplicant === false:
        exclude.push(Priority.NewApplicant, Priority.ExpeditedNewApplicant);
        break;
      case expedited === true && newApplicant === undefined:
        include.push(Priority.Expedited, Priority.ExpeditedNewApplicant);
        break;
      case expedited === false && newApplicant === undefined:
        exclude.push(Priority.Expedited, Priority.ExpeditedNewApplicant);
        break;
    }

    if (include.length > 0) filter.push({ terms: { case_priority: include } });
    if (exclude.length > 0) mustNot.push({ terms: { case_priority: exclude } });
  }

  /**
   * Filters cases based on referral status.
   */
  private applyReferralFilters(
    filter: estypes.QueryDslQueryContainer[],
    mustNot: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.referred !== undefined) {
      if (filters.referred) filter.push({ exists: { field: 'application_referral_id' } });
      if (!filters.referred) mustNot.push({ exists: { field: 'application_referral_id' } });
    }
  }

  /**
   * Filters cases by verification score levels using threshold ranges.
   */
  private applyVerificationFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.verified !== undefined) {
      const ranges = filters.verified.map((status) => {
        const { min, max } = VerificationThreshold[status];
        return {
          range: {
            application_verification_score: {
              gte: min,
              ...(max === 1 ? { lte: max } : { lt: max }),
            },
          },
        };
      });

      if (ranges.length > 0) {
        filter.push({
          bool: {
            should: ranges,
            minimum_should_match: 1,
          },
        });
      }
    }
  }

  /**
   * Filters cases by payment status. Automatically includes 'Initiated' when 'Success' is requested.
   */
  private applyPaymentStatusFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.paymentStatus?.length) {
      const paymentStatuses = filters.paymentStatus.includes(PaymentStatus.Success)
        ? [...new Set([...filters.paymentStatus, PaymentStatus.Initiated])]
        : filters.paymentStatus;

      filter.push({
        terms: {
          'payment_status.keyword': paymentStatuses,
        },
      });
    }
  }

  /**
   * Filters cases by associated tags using OR logic.
   */
  private applyTagFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.tags?.length) {
      filter.push({
        bool: {
          should: filters.tags.map((tag) => ({
            match: {
              case_tag_ids: tag,
            },
          })),
          minimum_should_match: 1,
        },
      });
    }
  }

  /**
   * Filters cases by specific answer key-value pairs using nested queries.
   */
  private applyAnswerFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.answerKey && filters?.answerValue) {
      filter.push({
        nested: {
          path: 'application_answers',
          query: {
            bool: {
              must: [
                {
                  term: {
                    'application_answers.key': filters.answerKey,
                  },
                },
                {
                  term: {
                    'application_answers.value.keyword': filters.answerValue,
                  },
                },
              ],
              minimum_should_match: 1,
            },
          },
        },
      });
    }
  }

  /**
   * Filters cases by answer review status.
   */
  private applyAnswerReviewFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.answerReviews?.length) {
      filter.push({
        bool: {
          should: filters.answerReviews.map((status) => ({
            match: { review_status: status },
          })),
          minimum_should_match: 1,
        },
      });
    }
  }

  /**
   * Filters cases by document tag types. Handles the TagType.value format conversion.
   */
  private applyDocumentTagFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.documentTagTypes?.length) {
      // Document tags types are stored as TagType.[value], e.g. `TagType.defect`, but they are
      // already split and processed in elasticsearch.
      const mappedTags = filters?.documentTagTypes
        .map((value) => value.split('.')?.[1])
        .filter(Boolean);
      if (mappedTags.length) {
        filter.push({ terms: { 'document_tag_types.keyword': mappedTags } });
      }
    }
  }

  /**
   * Filters cases by assignee ID, with special handling for unassigned cases.
   */
  private applyAssigneeFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.assigneeId?.length) {
      const hasNoneValue = filters.assigneeId.includes(NoAssignee);
      const validIds = hasNoneValue
        ? filters.assigneeId.filter((id) => id !== NoAssignee)
        : filters.assigneeId;

      if (hasNoneValue && validIds.length === 0) {
        filter.push({
          bool: {
            must_not: [{ exists: { field: 'assignee_id' } }],
          },
        });
      } else if (hasNoneValue) {
        filter.push({
          bool: {
            should: [
              { terms: { 'assignee_id.keyword': validIds } },
              { bool: { must_not: [{ exists: { field: 'assignee_id' } }] } },
            ],
            minimum_should_match: 1,
          },
        });
      } else {
        filter.push({
          terms: { 'assignee_id.keyword': validIds },
        });
      }
    }
  }

  /**
   * Filters cases by applicant type IDs.
   */
  private applyApplicantTypeFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.applicantTypeId?.length) {
      filter.push({
        terms: { case_applicant_type_ids: filters.applicantTypeId },
      });
    }
  }

  /**
   * Filters cases by case participant status with complex linking logic.
   */
  private applyCaseParticipantStatusFilters(
    filter: estypes.QueryDslQueryContainer[],
    filters?: CasesApplicationsFilters,
  ): void {
    if (filters?.caseParticipantStatus?.length) {
      const should: estypes.QueryDslQueryContainer[] = [];

      for (const status of filters.caseParticipantStatus) {
        switch (status) {
          case CaseParticipantStatus.Linked:
            should.push({
              bool: {
                must: [
                  { term: { has_pending_link: false } },
                  { term: { has_missing_participant: false } },
                  { term: { has_failed_link: false } },
                  { range: { case_participant_count: { gt: 1 } } },
                ],
              },
            });
            break;
          case CaseParticipantStatus.PendingLink:
            should.push({ term: { has_pending_link: true } });
            break;
          case CaseParticipantStatus.FailedLink:
            should.push({ term: { has_failed_link: true } });
            break;
          case CaseParticipantStatus.Unlinked:
            should.push({
              bool: {
                must: [
                  { term: { has_missing_participant: true } },
                  { term: { has_failed_link: false } },
                ],
              },
            });
            break;
        }
      }

      filter.push({
        bool: {
          should,
          minimum_should_match: 1,
        },
      });
    }
  }
}

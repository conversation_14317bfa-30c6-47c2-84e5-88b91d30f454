# Elasticsearch Repository

A production-ready, secure Elasticsearch search abstraction with mandatory tenant isolation and configurable pagination.

## Architecture Overview

```mermaid
graph TD
    A[ElasticsearchRepository] --> B[FilterBuilder]
    A --> C[SearchBuilder] 
    A --> D[SortBuilder]
    A --> E[SearchResultTransformer]
    
    B --> F[🔒 Security Filtering<br/>Partner Isolation]
    B --> G[📋 User Filters<br/>Status, Tags, etc.]
    
    C --> H[🔍 Search Queries<br/>Text & Categories]
    
    D --> I[📊 Column Sorting<br/>CreatedAt, Status, etc.]
    D --> J[⭐ Default Sorting<br/>Priority, DisplayID]
    
    E --> K[📄 Response Transform<br/>Typed Results]
    
    style F fill:#ffebee
    style B fill:#f3e5f5
    style A fill:#e8f5e8
```

## Design Principles

### 🔒 Security First
Every search operation **MUST** include partner/tenant filtering. This is enforced at:
- **Compile-time**: `FilterBuilder.applyPartnerFilter()` is required by interface
- **Runtime**: Repository throws if security filter implementation is missing
- **Query structure**: Security filters are always applied first, in the `filter` clause

### 🏗️ Index Agnostic
The repository orchestrates search logic but delegates index-specific concerns to specialized builders:
- **FilterBuilder**: Knows which fields to filter for each index
- **SearchBuilder**: Knows how to search specific field types  
- **SortBuilder**: Knows index-specific sort fields and defaults

### ⚡ Performance Conscious
- **Cursor-based pagination**: Uses `search_after` for efficient large dataset traversal
- **Optional PIT**: Point-in-Time contexts for consistency (disabled by default due to resource usage)
- **Query optimization**: Targeted field searches over expensive query_string when possible

### ⚙️ Configuration Driven
Pagination limits, PIT settings, and other behaviors are configurable per environment.

## Security Model

### Mandatory Tenant Isolation

Every index **MUST** implement secure partner filtering:

```typescript
class MyIndexFilterBuilder extends FilterBuilder<'my_index'> {
  // REQUIRED - Enforced at compile-time
  applyPartnerFilter(filter: QueryContainer[], partnerId: string): void {
    filter.push({ match: { my_partner_field: partnerId } });
  }
  
  // Optional - User-provided filters
  applyFilters(filter, mustNot, userFilters) {
    // Index-specific filter logic
  }
}
```

### Query Security Structure

All queries follow this security-first structure:

```json
{
  "bool": {
    "filter": [
      {"match": {"case_partner_id": "tenant123"}},  // ← Security (REQUIRED)
      {"terms": {"status": ["active", "pending"]}}  // ← User filters (optional)
    ],
    "must": [
      {"query_string": {"query": "*search terms*"}}  // ← Search (optional)
    ],
    "must_not": [
      {"exists": {"field": "deleted_at"}}            // ← Exclusions (optional)
    ]
  }
}
```

**Security Guarantees:**
- Partner filter is **always** applied first
- Missing security implementation = **runtime error**
- No bypass mechanism exists
- Audit trail via debug logging

## Performance Characteristics

### Pagination Strategy

Uses Elasticsearch's `search_after` for efficient cursor-based pagination:

```mermaid
sequenceDiagram
    participant C as Client
    participant R as Repository  
    participant E as Elasticsearch
    
    C->>R: search(page 1)
    R->>E: query + sort
    E-->>R: results + sort values
    R-->>C: {results, endCursor}
    
    C->>R: search(page 2, after: endCursor)
    R->>E: query + sort + search_after
    E-->>R: next results + sort values  
    R-->>C: {results, endCursor}
```

**Benefits:**
- ✅ Scales to millions of documents
- ✅ Consistent performance regardless of page depth
- ✅ No deep pagination penalty

**Trade-offs:**
- ❌ Cannot jump to arbitrary pages
- ❌ Total count not available (by design)

### Point-in-Time (PIT) Support

Optional consistency mechanism for long-running pagination:

| Without PIT | With PIT |
|-------------|----------|
| ⚡ Lower resource usage | 🔒 Consistent results |
| 📊 See real-time updates | 💾 Higher memory usage |
| ⚠️ Possible result skipping | ⏰ Results frozen in time |

```typescript
// Enable PIT for critical workflows
const repo = new ElasticsearchRepository({
  client, transformer,
  config: { 
    pit: { enabled: true, keepAlive: '2m' } 
  }
});
```

### Query Performance

| Query Type | Performance | Use Case |
|------------|-------------|----------|
| Category Search | ⚡ Fast | Exact field matching |
| Text Search | 🔄 Medium | Flexible text queries |
| Wildcard Search | ⚠️ Slow | Broad pattern matching |

## Error Handling Strategy

The repository uses **graceful degradation** with security exceptions:

```mermaid
graph TD
    A[Search Request] --> B{Security Filter Available?}
    B -->|No| C[💥 FAIL FAST<br/>Throw Error]
    B -->|Yes| D{Client Available?}
    D -->|No| E[⚠️ Empty Results<br/>Log Warning]
    D -->|Yes| F{Valid Pagination?}
    F -->|No| G[💥 Validation Error<br/>Throw Error]  
    F -->|Yes| H{Search Successful?}
    H -->|No| I[⚠️ Empty Results<br/>Log Error]
    H -->|Yes| J[✅ Return Results]
    
    style C fill:#ffebee
    style G fill:#ffebee
    style E fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#e8f5e8
```

**Error Categories:**
- 🚨 **Security errors**: Always bubble up (never fail silently)
- ⚠️ **Infrastructure errors**: Graceful degradation with logging
- 💥 **Validation errors**: Fast failure with helpful messages

## Extending for New Indices

Adding support for a new index requires implementing the builder pattern:

### 1. Create FilterBuilder (Required)

```typescript
export class MyIndexFilterBuilder extends FilterBuilder<'my_index'> {
  getIndex(): 'my_index' {
    return 'my_index';
  }
  
  // REQUIRED: Security filtering
  applyPartnerFilter(filter: QueryContainer[], partnerId: string): void {
    filter.push({ match: { my_partner_field: partnerId } });
  }
  
  // OPTIONAL: User filters
  applyFilters(filter, mustNot, userFilters) {
    if (userFilters?.status) {
      filter.push({ terms: { status: userFilters.status } });
    }
    // Add more filter logic...
  }
}
```

### 2. Create SearchBuilder (Optional)

```typescript
export class MyIndexSearchBuilder extends SearchBuilder<'my_index'> {
  getIndex(): 'my_index' {
    return 'my_index';
  }
  
  applySearch(must: QueryContainer[], args: SearchArguments<'my_index'>): void {
    const { search, searchCategories } = args.input;
    
    if (searchCategories?.length) {
      // Category-specific searches (faster)
      for (const category of searchCategories) {
        switch (category) {
          case 'Name':
            must.push({ match: { name: search } });
            break;
          case 'Email':
            must.push({ match: { email: search } });
            break;
        }
      }
    } else {
      // Global text search (slower but flexible)
      const should = [{ query_string: { query: `*${search}*` } }];
      must.push({ bool: { should } });
    }
  }
}
```

### 3. Create SortBuilder (Optional)

```typescript
export class MyIndexSortBuilder extends SortBuilder<'my_index'> {
  getIndex(): 'my_index' {
    return 'my_index';
  }
  
  applySort(sort: SortCombinations[], column: string, order: 'asc' | 'desc'): void {
    switch (column) {
      case 'CreatedAt':
        sort.push({ created_at: { order } });
        break;
      case 'Name':
        sort.push({ 'name.keyword': { order } });
        break;
      default:
        logger.warn({ column }, `Unknown sort column for my_index`);
    }
  }
  
  applyDefaultSort(sort: SortCombinations[], reverse?: boolean): void {
    const order = reverse ? 'desc' : 'asc';
    sort.push({ priority: { order } });
    sort.push({ 'created_at': { order } });
  }
}
```

### 4. Register Builders

Add to respective `index.ts` files:

```typescript
// filters/index.ts
function initializeBuilders(): void {
  builders.set('cases_applications', new CasesApplicationsFilterBuilder());
  builders.set('my_index', new MyIndexFilterBuilder()); // ← Add this
}

// search/index.ts  
function initializeBuilders(): void {
  builders.set('cases_applications', new CasesApplicationsSearchBuilder());
  builders.set('my_index', new MyIndexSearchBuilder()); // ← Add this
}

// sorts/index.ts
function initializeBuilders(): void {
  builders.set('cases_applications', new CasesApplicationsSortBuilder());  
  builders.set('my_index', new MyIndexSortBuilder()); // ← Add this
}
```

The repository will automatically discover and use your builders with full type safety.

## Configuration Reference

```typescript
interface ElasticsearchRepositoryConfig {
  pagination?: {
    defaultPageSize?: number;  // Default: 10, Range: 1-1000
    maxPageSize?: number;      // Default: 1000, Hard limit
  };
  pit?: {
    keepAlive?: string;        // Default: '1m', Format: '30s', '2m', '1h'
    enabled?: boolean;         // Default: false, Enable for consistency
  };
}
```

### Environment-Specific Configurations

```typescript
// Development - Smaller pages, PIT disabled
const devConfig = {
  pagination: { defaultPageSize: 10, maxPageSize: 100 },
  pit: { enabled: false }
};

// Production - Larger pages, PIT for critical workflows  
const prodConfig = {
  pagination: { defaultPageSize: 25, maxPageSize: 1000 },
  pit: { enabled: true, keepAlive: '2m' }
};

// Testing - Small pages, fast timeouts
const testConfig = {
  pagination: { defaultPageSize: 5, maxPageSize: 50 },
  pit: { enabled: false }
};
```

## Monitoring and Observability

### Key Metrics to Track

- **Search latency**: P50, P95, P99 response times
- **Error rates**: Security failures vs. infrastructure failures
- **PIT usage**: Active contexts and memory consumption
- **Query patterns**: Most common search terms and filters

### Debug Logging

All operations include structured debug logs:

```json
{
  "level": "debug",
  "message": "ElasticsearchRepository.queryBuilder: query",
  "token": {"partnerId": "partner123"},
  "args": {"input": {"index": "cases_applications"}},
  "query": {"bool": {"filter": [...]}}
}
```

**Log Levels:**
- `DEBUG`: Query details, execution flow
- `WARN`: Missing builders, infrastructure issues
- `ERROR`: Security failures, unexpected errors

## Migration Guide

### From Direct Elasticsearch Usage

```typescript
// Before: Direct client usage
const result = await esClient.search({
  index: 'cases_applications',
  body: {
    query: {
      bool: {
        filter: [
          { match: { case_partner_id: partnerId } }, // Manual security
          { terms: { status: ['active'] } }
        ]
      }
    }
  }
});

// After: Repository pattern  
const result = await repository.search(token, {
  input: {
    index: 'cases_applications',
    filters: { caseStatus: ['active'] }
  }
});
```

### From Legacy Search Implementation

1. **Extract security filtering** → Implement `FilterBuilder.applyPartnerFilter()`
2. **Extract search logic** → Implement `SearchBuilder.applySearch()` 
3. **Extract sorting logic** → Implement `SortBuilder.applySort()`
4. **Update client code** → Use repository instead of direct queries
5. **Add configuration** → Set pagination and PIT settings

## Troubleshooting

### Common Issues

**Security Filter Missing**
```
Error: Security filter implementation required for index: my_index
```
→ Implement `FilterBuilder.applyPartnerFilter()` for your index

**Pagination Validation Failed**
```
Error: Page size must be between 1 and 1000
```
→ Check `pagination.take` value or adjust `config.pagination.maxPageSize`

**Builder Not Found**
```
WARN: No search builder registered for index: my_index. Using generic search builder.
```
→ Add your SearchBuilder to `search/index.ts`

### Performance Issues

**Slow Text Searches**
- Use category searches instead of global text search
- Implement targeted field matching in SearchBuilder
- Consider adding edge n-gram fields for faster prefix matching

**High Memory Usage**
- Disable PIT if consistency isn't critical
- Reduce `keepAlive` duration for PIT contexts
- Lower `maxPageSize` to reduce result set sizes

**Deep Pagination Timeouts**
- Repository uses `search_after` to avoid this issue
- If still occurring, check for missing sort fields or misconfigured cursors

---

## Next Steps

- See [api.md](./api.md) for detailed usage examples
- See [CONTRIBUTING.md](./CONTRIBUTING.md) for development guidelines
- See [SECURITY.md](./SECURITY.md) for security best practices
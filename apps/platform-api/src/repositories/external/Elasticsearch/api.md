# Elasticsearch Repository API

Detailed usage examples and configuration options for the ElasticsearchRepository.

## Table of Contents
- [Quick Start](#quick-start)
- [Basic Usage](#basic-usage)
- [Advanced Searching](#advanced-searching)
- [Pagination](#pagination)
- [Configuration](#configuration)
- [Error Handling](#error-handling)
- [Performance Optimization](#performance-optimization)

## Quick Start

```typescript
import { ElasticsearchRepository } from './ElasticsearchRepository';

// Basic setup
const repository = new ElasticsearchRepository({
  client: elasticsearchClient,
  transformer: searchResultTransformer
});

// Simple search
const results = await repository.search(adminToken, {
  input: { 
    index: 'cases_applications', 
    search: 'john doe' 
  }
});
```

## Basic Usage

### Health Check

```typescript
const isHealthy = await repository.healthCheck();
if (!isHealthy) {
  console.error('Elasticsearch cluster is not available');
}
```

### Simple Text Search

```typescript
// Search across all text fields
const results = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: 'emergency assistance'
  }
});

console.log(`Found ${results.nodes.length} results`);
results.nodes.forEach(node => {
  console.log(`Case ${node.caseDisplayId}: ${node.submitterName}`);
});
```

### Category-Specific Search

```typescript
// Search specific fields for better performance
const results = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: '<EMAIL>',
    searchCategories: ['SubmitterEmail'] // Faster than global search
  }
});
```

### Multiple Category Search

```typescript
// Search across multiple specific fields
const results = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: 'john',
    searchCategories: ['SubmitterName', 'SubmitterEmail', 'ProgramName']
  }
});
```

## Advanced Searching

### Filtering

```typescript
// Apply filters to narrow results
const results = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: 'emergency',
    filters: {
      caseStatus: ['pending', 'in_review'],
      expedited: true,
      verified: ['high_confidence'],
      paymentStatus: ['pending']
    }
  }
});
```

### Complex Filter Combinations

```typescript
// Multiple filter types
const results = await repository.search(token, {
  input: {
    index: 'cases_applications',
    filters: {
      // Status filtering
      caseStatus: ['active', 'pending'],
      
      // Priority filtering  
      expedited: true,
      newApplicant: false,
      
      // Verification filtering
      verified: ['high_confidence', 'medium_confidence'],
      
      // Assignment filtering
      assigneeId: ['user123', 'user456'],
      
      // Program filtering
      programId: 'prog123',
      
      // Tag filtering
      tags: ['urgent', 'review_required'],
      
      // Date range filtering (if supported by your FilterBuilder)
      dateRange: {
        field: 'created_at',
        start: '2024-01-01',
        end: '2024-12-31'
      }
    }
  }
});
```

### Sorting

```typescript
// Sort by specific column
const results = await repository.search(token, {
  input: { index: 'cases_applications' },
  sort: {
    column: 'CreatedAt',
    direction: SortDirection.Descending
  }
});

// Available sort columns for cases_applications:
// - CreatedAt
// - UpdatedAt  
// - SubmittedAt
// - ApplicantName
// - Assignee
// - Status
// - AwardedAmount
// - ProgramName
```

### Including Full Data

```typescript
// Include full document data (not just metadata)
const results = await repository.search(token, {
  input: { 
    index: 'cases_applications',
    search: 'john doe'
  },
  includeData: true // Returns full document content
});
```

## Pagination

### Forward Pagination

```typescript
// First page
const firstPage = await repository.search(token, {
  input: { index: 'cases_applications' },
  pagination: { take: 20 }
});

console.log(`Page 1: ${firstPage.nodes.length} results`);
console.log(`Has next page: ${firstPage.pageInfo.hasNextPage}`);

// Next page
if (firstPage.pageInfo.hasNextPage) {
  const secondPage = await repository.search(token, {
    input: { index: 'cases_applications' },
    pagination: { 
      take: 20,
      after: firstPage.pageInfo.endCursor 
    }
  });
}
```

### Backward Pagination

```typescript
// Navigate backwards from current cursor
const previousPage = await repository.search(token, {
  input: { index: 'cases_applications' },
  pagination: {
    take: 20,
    before: currentCursor // Goes backward from this point
  }
});

console.log(`Has previous page: ${previousPage.pageInfo.hasPreviousPage}`);
```

### Large Page Sizes

```typescript
// Request larger pages (up to configured maximum)
const largePage = await repository.search(token, {
  input: { index: 'cases_applications' },
  pagination: { take: 100 } // May be limited by maxPageSize config
});
```

### Complete Pagination Example

```typescript
async function getAllResults(token: AdminToken, searchArgs: SearchArguments<'cases_applications'>) {
  const allResults = [];
  let cursor: string | undefined;
  let hasMore = true;
  
  while (hasMore) {
    const page = await repository.search(token, {
      ...searchArgs,
      pagination: { 
        take: 50,
        ...(cursor && { after: cursor })
      }
    });
    
    allResults.push(...page.nodes);
    hasMore = page.pageInfo.hasNextPage;
    cursor = page.pageInfo.endCursor;
    
    console.log(`Fetched ${page.nodes.length} more results (total: ${allResults.length})`);
  }
  
  return allResults;
}
```

## Configuration

### Basic Configuration

```typescript
const repository = new ElasticsearchRepository({
  client: elasticsearchClient,
  transformer: searchResultTransformer,
  config: {
    pagination: {
      defaultPageSize: 25,  // Default page size when not specified
      maxPageSize: 500      // Maximum allowed page size
    },
    pit: {
      enabled: false,       // Enable Point-in-Time for consistency
      keepAlive: '1m'       // How long PIT contexts stay alive
    }
  }
});
```

### Environment-Specific Configuration

```typescript
// Development environment
const devRepository = new ElasticsearchRepository({
  client, transformer,
  config: {
    pagination: { defaultPageSize: 10, maxPageSize: 100 },
    pit: { enabled: false } // Disabled for faster development
  }
});

// Production environment  
const prodRepository = new ElasticsearchRepository({
  client, transformer,
  config: {
    pagination: { defaultPageSize: 25, maxPageSize: 1000 },
    pit: { enabled: true, keepAlive: '2m' } // Enabled for consistency
  }
});

// Testing environment
const testRepository = new ElasticsearchRepository({
  client, transformer,
  config: {
    pagination: { defaultPageSize: 5, maxPageSize: 20 },
    pit: { enabled: false } // Disabled for test speed
  }
});
```

### Configuration from Environment Variables

```typescript
const repository = new ElasticsearchRepository({
  client, transformer,
  config: {
    pagination: {
      defaultPageSize: parseInt(process.env.ES_DEFAULT_PAGE_SIZE || '10'),
      maxPageSize: parseInt(process.env.ES_MAX_PAGE_SIZE || '1000')
    },
    pit: {
      enabled: process.env.ES_PIT_ENABLED === 'true',
      keepAlive: process.env.ES_PIT_KEEP_ALIVE || '1m'
    }
  }
});
```

## Error Handling

### Basic Error Handling

```typescript
try {
  const results = await repository.search(token, searchArgs);
  return results;
} catch (error) {
  if (error.message.includes('Partner ID is required')) {
    // Security error - token is invalid
    throw new AuthenticationError('Invalid authentication token');
  }
  
  if (error.message.includes('Page size must be between')) {
    // Validation error - fix pagination
    throw new ValidationError('Invalid pagination parameters');
  }
  
  // Log unexpected errors
  logger.error({ error, searchArgs }, 'Search failed unexpectedly');
  throw error;
}
```

### Graceful Degradation

```typescript
async function searchWithFallback(token: AdminToken, searchArgs: SearchArguments<any>) {
  try {
    // Primary search
    return await repository.search(token, searchArgs);
  } catch (error) {
    if (error.message.includes('Security filter implementation required')) {
      // Index not supported yet - return empty results
      logger.warn({ index: searchArgs.input.index }, 'Index not yet supported');
      return { nodes: [], pageInfo: { hasNextPage: false, hasPreviousPage: false } };
    }
    
    // Re-throw other errors
    throw error;
  }
}
```

### Validation Helpers

```typescript
function validateSearchArgs<I extends Index>(args: SearchArguments<I>): void {
  if (!args.input?.index) {
    throw new Error('Index is required');
  }
  
  if (args.pagination?.take && args.pagination.take > 1000) {
    throw new Error('Page size too large');
  }
  
  if (args.pagination?.after && args.pagination?.before) {
    throw new Error('Cannot specify both after and before cursors');
  }
}
```

## Performance Optimization

### Choosing Search Strategies

```typescript
// ⚡ FAST: Category search (recommended)
const fastSearch = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: '<EMAIL>',
    searchCategories: ['SubmitterEmail'] // Targets specific field
  }
});

// 🔄 MEDIUM: Multiple category search
const mediumSearch = await repository.search(token, {
  input: {
    index: 'cases_applications', 
    search: 'john',
    searchCategories: ['SubmitterName', 'SubmitterEmail'] // Multiple fields
  }
});

// ⚠️ SLOW: Global text search (use sparingly)
const slowSearch = await repository.search(token, {
  input: {
    index: 'cases_applications',
    search: 'john' // Searches ALL text fields
  }
});
```

### Pagination Best Practices

```typescript
// ✅ Good: Reasonable page sizes
const goodPagination = { take: 25 }; // Fast and responsive

// ⚠️ Acceptable: Larger pages for batch processing
const largePagination = { take: 100 }; // Slower but fewer requests

// ❌ Bad: Excessive page sizes
const badPagination = { take: 1000 }; // Very slow, high memory usage
```

### Filtering for Performance

```typescript
// ✅ Good: Specific filters first
const efficientFilters = {
  programId: 'prog123',        // Exact match - very fast
  caseStatus: ['pending'],     // Terms query - fast
  expedited: true,             // Boolean - fast
  search: 'emergency'          // Text search - slower
};

// ❌ Bad: Only text search without filters
const inefficientSearch = {
  search: 'emergency' // Will scan many documents
};
```

### Point-in-Time (PIT) Usage

```typescript
// Use PIT for consistency during long-running pagination
const consistentRepository = new ElasticsearchRepository({
  client, transformer,
  config: { pit: { enabled: true, keepAlive: '5m' } }
});

// PIT is beneficial when:
// - Paginating through large datasets
// - Results must be consistent across pages  
// - Data is actively being modified
// - User expects stable ordering

// Skip PIT when:
// - Single page requests
// - Real-time data is preferred
// - Memory usage is a concern
// - Fast response times are critical
```

### Monitoring Performance

```typescript
async function searchWithMetrics<I extends Index>(
  token: AdminToken, 
  args: SearchArguments<I>
) {
  const startTime = Date.now();
  
  try {
    const results = await repository.search(token, args);
    const duration = Date.now() - startTime;
    
    // Log performance metrics
    logger.info({
      index: args.input.index,
      searchType: args.input.searchCategories ? 'category' : 'global',
      pageSize: args.pagination?.take || 10,
      resultCount: results.nodes.length,
      duration
    }, 'Search completed');
    
    return results;
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error({
      index: args.input.index,
      duration,
      error: error.message
    }, 'Search failed');
    throw error;
  }
}
```

## Advanced Usage Patterns

### Search Builder Pattern

```typescript
class SearchRequestBuilder<I extends Index> {
  private args: Partial<SearchArguments<I>> = {};
  
  forIndex(index: I): this {
    this.args.input = { ...this.args.input, index };
    return this;
  }
  
  withSearch(search: string, categories?: string[]): this {
    this.args.input = {
      ...this.args.input,
      search,
      searchCategories: categories
    };
    return this;
  }
  
  withFilters(filters: any): this {
    this.args.input = { ...this.args.input, filters };
    return this;
  }
  
  withPagination(pagination: any): this {
    this.args.pagination = pagination;
    return this;
  }
  
  withSort(column: string, direction: SortDirection): this {
    this.args.sort = { column, direction };
    return this;
  }
  
  build(): SearchArguments<I> {
    return this.args as SearchArguments<I>;
  }
}

// Usage
const searchArgs = new SearchRequestBuilder<'cases_applications'>()
  .forIndex('cases_applications')
  .withSearch('emergency', ['ProgramName'])
  .withFilters({ caseStatus: ['pending'] })
  .withPagination({ take: 25 })
  .withSort('CreatedAt', SortDirection.Descending)
  .build();

const results = await repository.search(token, searchArgs);
```

### Batch Search Operations

```typescript
async function batchSearch(
  searches: Array<{ id: string; args: SearchArguments<any> }>
): Promise<Array<{ id: string; results: SearchResult<any> }>> {
  const results = await Promise.allSettled(
    searches.map(async ({ id, args }) => ({
      id,
      results: await repository.search(token, args)
    }))
  );
  
  return results
    .filter((result): result is PromiseFulfilledResult<any> => 
      result.status === 'fulfilled')
    .map(result => result.value);
}

// Usage
const batchResults = await batchSearch([
  { id: 'pending-cases', args: { input: { index: 'cases_applications', filters: { caseStatus: ['pending'] } } } },
  { id: 'urgent-cases', args: { input: { index: 'cases_applications', filters: { expedited: true } } } },
  { id: 'recent-cases', args: { input: { index: 'cases_applications' }, sort: { column: 'CreatedAt', direction: SortDirection.Descending } } }
]);
```

### Caching Strategy

```typescript
class CachedSearchRepository {
  private cache = new Map<string, { result: SearchResult<any>; timestamp: number }>();
  private readonly ttl = 5 * 60 * 1000; // 5 minutes
  
  constructor(private readonly repository: ElasticsearchRepository) {}
  
  async search<I extends Index>(
    token: AdminToken, 
    args: SearchArguments<I>
  ): Promise<SearchResult<I>> {
    // Only cache simple searches without pagination
    if (args.pagination?.after || args.pagination?.before) {
      return this.repository.search(token, args);
    }
    
    const cacheKey = this.buildCacheKey(token.partnerId, args);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.result;
    }
    
    const result = await this.repository.search(token, args);
    this.cache.set(cacheKey, { result, timestamp: Date.now() });
    
    return result;
  }
  
  private buildCacheKey(partnerId: string, args: SearchArguments<any>): string {
    return `${partnerId}:${JSON.stringify(args)}`;
  }
}
```

---

## Summary

The ElasticsearchRepository provides a powerful, secure, and performant interface for searching across multiple indices. Key takeaways:

- **Always use category searches** when you know which fields to search
- **Apply filters** to narrow results before text searching  
- **Use reasonable page sizes** (10-50 for UI, 100+ for batch processing)
- **Enable PIT** only when consistency is critical
- **Handle errors gracefully** with appropriate fallbacks
- **Monitor performance** to identify optimization opportunities

For implementation details and architectural decisions, see [README.md](./README.md).
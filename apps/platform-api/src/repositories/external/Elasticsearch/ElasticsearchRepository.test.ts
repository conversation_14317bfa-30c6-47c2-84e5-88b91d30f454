import {
  CaseParticipantStatus,
  CaseStatus,
  PaymentStatus,
  Priority,
  SearchArguments,
  VerificationStatus,
} from '@bybeam/platform-types';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ElasticsearchRepository } from './ElasticsearchRepository.js';
import { ElasticsearchClient } from './client/ElasticsearchClient.js';
import { SearchResultTransformer } from './transformer/SearchResultTransformer.js';

const { createCursor, extractCursor, isValidCursor, DATE_MAX, DATE_MIN } = vi.hoisted(() => ({
  createCursor: vi.fn().mockReturnValue('mockCursor'),
  extractCursor: vi.fn().mockReturnValue({ sort: ['sortValue1', 'sortValue2'] }),
  isValidCursor: vi.fn(),
  DATE_MAX: 'CURSOR_DATE_MAX',
  DATE_MIN: 'CURSOR_DATE_MIN',
}));

vi.mock('./utils/cursor', () => ({
  createCursor,
  extractCursor,
  isValidCursor,
  DATE_MAX,
  DATE_MIN,
}));

describe('ElasticsearchRepository', () => {
  const originalEnv = { ...process.env };

  beforeEach(() => {
    vi.useFakeTimers();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
    process.env = { ...originalEnv };
  });

  describe('healthCheck', () => {
    it('should return true if the client service health check is green', async () => {
      const repo = new ElasticsearchRepository({
        client: {
          healthCheck: vi.fn().mockResolvedValue(true),
        } as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });

      const result = await repo.healthCheck();

      expect(result).toBe(true);
    });

    it('should return false if the client service health check is not green', async () => {
      const repo = new ElasticsearchRepository({
        client: {
          healthCheck: vi.fn().mockResolvedValue(false),
        } as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });

      const result = await repo.healthCheck();

      expect(result).toBe(false);
    });

    it('should handle errors during health check', async () => {
      const error = new Error('Health check failed');
      const mockHealthCheck = vi.fn().mockRejectedValue(error);

      const repo = new ElasticsearchRepository({
        client: {
          healthCheck: mockHealthCheck,
        } as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });

      const result = await repo.healthCheck();

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error },
        'ElasticsearchRepository.healthCheck: unexpected error',
      );
    });
  });

  describe('queryBuilder', () => {
    it('should build required filters for all searches', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: { index: 'cases_applications' },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should include archived cases when archived is provided and true', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: { archived: true },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [],
        },
      });
    });

    it('should add program filter when programId is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            programId: 'mockProgramId',
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { match: { case_program_id: 'mockProgramId' } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add caseStatus filter when caseStatus is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            caseStatus: [CaseStatus.InReview, CaseStatus.InProgress],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { terms: { 'case_status.keyword': ['InReview', 'InProgress'] } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add a simple search query when search is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [
            {
              bool: {
                should: [
                  { query_string: { query: '*searchTerm*' } },
                  {
                    nested: {
                      path: 'application_answers',
                      query: { match: { 'application_answers.value.edge': 'searchterm' } },
                    },
                  },
                ],
              },
            },
          ],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add a deep search query when deepSearch is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          deepSearch: true,
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [
            {
              bool: {
                should: [
                  { query_string: { query: '*searchTerm*' } },
                  {
                    nested: {
                      path: 'application_answers',
                      query: {
                        bool: {
                          should: [{ wildcard: { 'application_answers.value': '*searchTerm*' } }],
                        },
                      },
                    },
                  },
                ],
              },
            },
          ],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add application_display_id match when ApplicationId search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['ApplicationId'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ match: { application_display_id: 'searchTerm' } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add case_display_id match when CaseId search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['CaseId'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ match: { case_display_id: 'searchTerm' } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add submitter_name fuzzy match when SubmitterName search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['SubmitterName'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ fuzzy: { submitter_name: { value: 'searchTerm', fuzziness: 'AUTO' } } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add submitter_email fuzzy match when SubmitterEmail search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['SubmitterEmail'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ fuzzy: { submitter_email: { value: 'searchTerm', fuzziness: 'AUTO' } } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add submitter_phone fuzzy match when SubmitterPhone search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['SubmitterPhone'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ fuzzy: { submitter_phone: { value: 'searchTerm', fuzziness: 'AUTO' } } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add submitter_display_id match when SubmitterDisplayId search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['SubmitterDisplayId'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ match: { submitter_display_id: 'searchTerm' } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add case_program_name fuzzy match when ProgramName search category is provided', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          search: 'searchTerm',
          searchCategories: ['ProgramName'],
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: checking private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [{ fuzzy: { case_program_name: { value: 'searchTerm', fuzziness: 'AUTO' } } }],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add priority filters when expedited is true', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            expedited: true,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { terms: { case_priority: [Priority.Expedited, Priority.ExpeditedNewApplicant] } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add priority filters when expedited is false', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            expedited: false,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [
            { exists: { field: 'case_deactivated_at' } },
            { terms: { case_priority: [Priority.Expedited, Priority.ExpeditedNewApplicant] } },
          ],
        },
      });
    });

    it('should add priority filters when newApplicant is true', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            newApplicant: true,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { terms: { case_priority: [Priority.NewApplicant, Priority.ExpeditedNewApplicant] } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add priority filters when both expedited and newApplicant are true', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            expedited: true,
            newApplicant: true,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { terms: { case_priority: [Priority.ExpeditedNewApplicant] } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add priority filters when both expedited and newApplicant are false', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            expedited: false,
            newApplicant: false,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [
            { exists: { field: 'case_deactivated_at' } },
            {
              terms: {
                case_priority: [
                  Priority.ExpeditedNewApplicant,
                  Priority.Expedited,
                  Priority.NewApplicant,
                ],
              },
            },
          ],
        },
      });
    });

    it('should add referred filter when referred is true', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            referred: true,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { exists: { field: 'application_referral_id' } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add referred filter when referred is false', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            referred: false,
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
          must_not: [
            { exists: { field: 'case_deactivated_at' } },
            { exists: { field: 'application_referral_id' } },
          ],
        },
      });
    });

    it('should add verification filter for a single verification status', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            verified: [VerificationStatus.PartiallyVerified],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              bool: {
                should: [
                  {
                    range: {
                      application_verification_score: {
                        gte: 0.4,
                        lt: 0.9,
                      },
                    },
                  },
                ],
                minimum_should_match: 1,
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add verification filter for multiple verification statuses', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            verified: [VerificationStatus.Unverified, VerificationStatus.Verified],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              bool: {
                should: [
                  {
                    range: {
                      application_verification_score: {
                        gte: 0,
                        lt: 0.4,
                      },
                    },
                  },
                  {
                    range: {
                      application_verification_score: {
                        gte: 0.9,
                        lte: 1,
                      },
                    },
                  },
                ],
                minimum_should_match: 1,
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add payment status filter including Initiated when Success is specified', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            paymentStatus: [PaymentStatus.Success],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              terms: {
                'payment_status.keyword': [PaymentStatus.Success, PaymentStatus.Initiated],
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add payment status filter without modification for non-Success statuses', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            paymentStatus: [PaymentStatus.Failed],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              terms: {
                'payment_status.keyword': [PaymentStatus.Failed],
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should handle multiple payment statuses including Success', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            paymentStatus: [PaymentStatus.Success, PaymentStatus.Failed],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              terms: {
                'payment_status.keyword': [
                  PaymentStatus.Success,
                  PaymentStatus.Failed,
                  PaymentStatus.Initiated,
                ],
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('should add tag filter that matches any of the provided UUIDs', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            tags: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d7-9876-************'],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              bool: {
                should: [
                  {
                    match: {
                      case_tag_ids: '123e4567-e89b-12d3-a456-************',
                    },
                  },
                  {
                    match: {
                      case_tag_ids: '987fcdeb-51a2-43d7-9876-************',
                    },
                  },
                ],
                minimum_should_match: 1,
              },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });

    it('adds a nested filter for applicationAnswers', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            answerKey: 'question1',
            answerValue: 'answer1',
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          filter: [
            {
              match: {
                case_partner_id: 'mockPartnerId',
              },
            },
            {
              nested: {
                path: 'application_answers',
                query: {
                  bool: {
                    must: [
                      { term: { 'application_answers.key': 'question1' } },
                      { term: { 'application_answers.value.keyword': 'answer1' } },
                    ],
                    minimum_should_match: 1,
                  },
                },
              },
            },
          ],
          must: [],
          must_not: [
            {
              exists: {
                field: 'case_deactivated_at',
              },
            },
          ],
        },
      });
    });

    describe('answerReviews', () => {
      it('should add an answer review filter when present', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              answerReviews: ['NEEDS_RESUBMISSION'],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  minimum_should_match: 1,
                  should: [{ match: { review_status: 'NEEDS_RESUBMISSION' } }],
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should not add an answer review filter for an empty array', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: { index: 'cases_applications', filters: { answerReviews: [] } },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [{ match: { case_partner_id: 'mockPartnerId' } }],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
    });

    it('should add a document tag filter', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            documentTagTypes: ['TagType.defect', 'BogusTagType'],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            { terms: { 'document_tag_types.keyword': ['defect'] } },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });
    describe('assignee ID filter', () => {
      it('should add an assignee id filter when the values are IDs', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              assigneeId: ['mockAdminId1', 'mockAdminId2'],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              { terms: { 'assignee_id.keyword': ['mockAdminId1', 'mockAdminId2'] } },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add an assignee id filter when the values are IDs and "none"', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              assigneeId: ['mockAdminId1', 'mockAdminId2', 'none'],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [
                    { terms: { 'assignee_id.keyword': ['mockAdminId1', 'mockAdminId2'] } },
                    { bool: { must_not: [{ exists: { field: 'assignee_id' } }] } },
                  ],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add an assignee id filter when the values are "none"', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              assigneeId: ['none'],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              { bool: { must_not: [{ exists: { field: 'assignee_id' } }] } },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
    });

    describe('caseParticipantStatus filter', () => {
      it('should add a case participant status filter for Linked', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              caseParticipantStatus: [CaseParticipantStatus.Linked],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must: [
                          { term: { has_pending_link: false } },
                          { term: { has_missing_participant: false } },
                          { term: { has_failed_link: false } },
                          { range: { case_participant_count: { gt: 1 } } },
                        ],
                      },
                    },
                  ],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add a case participant status filter for PendingLink', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              caseParticipantStatus: [CaseParticipantStatus.PendingLink],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [{ term: { has_pending_link: true } }],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add a case participant status filter for FailedLink', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              caseParticipantStatus: [CaseParticipantStatus.FailedLink],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [{ term: { has_failed_link: true } }],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add a case participant status filter for Unlinked', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              caseParticipantStatus: [CaseParticipantStatus.Unlinked],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must: [
                          { term: { has_missing_participant: true } },
                          { term: { has_failed_link: false } },
                        ],
                      },
                    },
                  ],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
      it('should add a case participant status filter for multiple statuses', () => {
        const repo = new ElasticsearchRepository({
          client: {} as unknown as ElasticsearchClient,
          transformer: {} as unknown as SearchResultTransformer,
        });
        const token = { partnerId: 'mockPartnerId' } as AdminToken;
        const args = {
          input: {
            index: 'cases_applications',
            filters: {
              caseParticipantStatus: [
                CaseParticipantStatus.Linked,
                CaseParticipantStatus.PendingLink,
                CaseParticipantStatus.FailedLink,
                CaseParticipantStatus.Unlinked,
              ],
            },
          },
        } as SearchArguments<'cases_applications'>;

        // biome-ignore lint/complexity/useLiteralKeys: private property
        const query = repo['queryBuilder'](token, args);

        expect(query).toEqual({
          bool: {
            must: [],
            filter: [
              { match: { case_partner_id: 'mockPartnerId' } },
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must: [
                          { term: { has_pending_link: false } },
                          { term: { has_missing_participant: false } },
                          { term: { has_failed_link: false } },
                          { range: { case_participant_count: { gt: 1 } } },
                        ],
                      },
                    },
                    { term: { has_pending_link: true } },
                    { term: { has_failed_link: true } },
                    {
                      bool: {
                        must: [
                          { term: { has_missing_participant: true } },
                          { term: { has_failed_link: false } },
                        ],
                      },
                    },
                  ],
                  minimum_should_match: 1,
                },
              },
            ],
            must_not: [{ exists: { field: 'case_deactivated_at' } }],
          },
        });
      });
    });

    it('should add an applicant type ID filter', () => {
      const repo = new ElasticsearchRepository({
        client: {} as unknown as ElasticsearchClient,
        transformer: {} as unknown as SearchResultTransformer,
      });
      const token = { partnerId: 'mockPartnerId' } as AdminToken;
      const args = {
        input: {
          index: 'cases_applications',
          filters: {
            applicantTypeId: ['mockApplicantTypeId1', 'mockApplicantTypeId2'],
          },
        },
      } as SearchArguments<'cases_applications'>;

      // biome-ignore lint/complexity/useLiteralKeys: private property
      const query = repo['queryBuilder'](token, args);

      expect(query).toEqual({
        bool: {
          must: [],
          filter: [
            { match: { case_partner_id: 'mockPartnerId' } },
            {
              terms: { case_applicant_type_ids: ['mockApplicantTypeId1', 'mockApplicantTypeId2'] },
            },
          ],
          must_not: [{ exists: { field: 'case_deactivated_at' } }],
        },
      });
    });
  });
});

describe('search', () => {
  it('should return an empty array if the client is not available', async () => {
    const repo = new ElasticsearchRepository({
      client: {
        getClient: vi.fn().mockResolvedValue(null),
      } as unknown as ElasticsearchClient,
      transformer: {
        createEmptyResult: vi.fn().mockReturnValue({
          nodes: [],
          pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
        }),
      } as unknown as SearchResultTransformer,
    });

    const result = await repo.search(
      { partnerId: 'mockPartnerId' } as AdminToken,
      { input: { index: 'cases_applications' } } as SearchArguments<'cases_applications'>,
    );

    expect(result).toEqual({
      nodes: [],
      pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
    });
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'ElasticsearchRepository.search: Elasticsearch client not available',
    );
  });

  it('should log an error and return an empty array if there is no partnerId on the token', async () => {
    const repo = new ElasticsearchRepository({
      client: {
        getClient: vi.fn().mockResolvedValue({}), // Return a mock client
      } as unknown as ElasticsearchClient,
      transformer: {
        createEmptyResult: vi.fn().mockReturnValue({
          nodes: [],
          pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
        }),
      } as unknown as SearchResultTransformer,
    });

    const result = await repo.search(
      {} as AdminToken, // Missing partnerId
      { input: { index: 'cases_applications' } } as SearchArguments<'cases_applications'>,
    );

    expect(result).toEqual({
      nodes: [],
      pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
    });
    expect(mockLogger.error).toHaveBeenCalledWith(
      { error: new Error('no partnerId on token') },
      'ElasticsearchRepository.search: unexpected error',
    );
  });

  it('should log an error and return an empty array if there is no index provided in the args', async () => {
    const repo = new ElasticsearchRepository({
      client: {
        getClient: vi.fn().mockResolvedValue({}), // Return a mock client
      } as unknown as ElasticsearchClient,
      transformer: {
        createEmptyResult: vi.fn().mockReturnValue({
          nodes: [],
          pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
        }),
      } as unknown as SearchResultTransformer,
    });

    const result = await repo.search(
      { partnerId: 'mockPartnerId' } as AdminToken,
      { input: {} } as SearchArguments<'cases_applications'>, // Missing index
    );

    expect(result).toEqual({
      nodes: [],
      pageInfo: { count: 0, hasNextPage: false, hasPreviousPage: false },
    });
    expect(mockLogger.error).toHaveBeenCalledWith(
      { error: new Error('no index provided in args') },
      'ElasticsearchRepository.search: unexpected error',
    );
  });

  it('should return the results of the search if the client is initialized and the input is validated', async () => {
    // Create a mockClient with a search method
    const mockClient = {
      search: vi.fn().mockResolvedValue({
        hits: { hits: [{ _id: 'mockId', _source: { mockKey: 'mockValue' } }], total: 1 },
      }),
    };

    const repo = new ElasticsearchRepository({
      client: {
        getClient: vi.fn().mockResolvedValue(mockClient),
      } as unknown as ElasticsearchClient,
      transformer: {
        transform: vi.fn().mockReturnValue({
          nodes: [{ id: 'mockId', data: { mockKey: 'mockValue' } }],
          pageInfo: {
            count: 1,
            hasNextPage: false,
            hasPreviousPage: false,
            endCursor: undefined,
            startCursor: undefined,
          },
          metadata: { hits: undefined },
        }),
      } as unknown as SearchResultTransformer,
    });

    const result = await repo.search(
      { partnerId: 'mockPartnerId' } as AdminToken,
      { input: { index: 'cases_applications' } } as SearchArguments<'cases_applications'>,
    );

    expect(mockClient.search).toHaveBeenCalledTimes(1);
    expect(result).toEqual({
      nodes: [{ id: 'mockId', data: { mockKey: 'mockValue' } }],
      pageInfo: {
        count: 1,
        hasNextPage: false,
        hasPreviousPage: false,
        endCursor: undefined,
        startCursor: undefined,
      },
      metadata: { hits: undefined },
    });
  });

  it('should include search_after in the search options when provided', async () => {
    const mockSearch = vi.fn().mockResolvedValue({
      hits: { hits: [{ _id: 'mockId', sort: ['sortValue1', 'sortValue2'] }], total: 1 },
    });

    const mockClient = { search: mockSearch };

    const repo = new ElasticsearchRepository({
      client: {
        getClient: vi.fn().mockResolvedValue(mockClient),
      } as unknown as ElasticsearchClient,
      transformer: {
        transform: vi.fn().mockReturnValue({
          nodes: [{ id: 'mockId', data: { mockKey: 'mockValue' } }],
          pageInfo: {
            count: 1,
            hasNextPage: false,
            hasPreviousPage: false,
            endCursor: undefined,
            startCursor: undefined,
          },
          metadata: { hits: undefined },
        }),
      } as unknown as SearchResultTransformer,
    });

    isValidCursor.mockReturnValue(true);

    await repo.search(
      { partnerId: 'mockPartnerId' } as AdminToken,
      {
        input: { index: 'cases_applications' },
        pagination: {
          take: 20,
          direction: 'forward',
          after: 'mockCursor',
        },
      } as SearchArguments<'cases_applications'>,
    );

    expect(mockSearch).toHaveBeenCalledTimes(1);
    expect(extractCursor).toHaveBeenCalledWith('mockCursor');
    expect(mockSearch).toHaveBeenCalledWith(
      expect.objectContaining({
        index: 'cases_applications',
        search_after: ['sortValue1', 'sortValue2'],
        size: 21,
      }),
    );
  });
});

describe('sortBuilder', () => {
  it('should sort by case_priority and case_display_id by default', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by case_created_at when CaseCreatedAt is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'CreatedAt',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      {
        case_created_at: {
          order: 'asc',
          format: 'strict_date_optional_time',
          missing: 'CURSOR_DATE_MAX',
        },
      },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by case_status_updated_at when CaseUpdatedAt is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'UpdatedAt',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      {
        case_status_updated_at: {
          order: 'asc',
          format: 'strict_date_optional_time',
          missing: 'CURSOR_DATE_MAX',
        },
      },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by application_submitted_at when CaseSubmittedAt is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'SubmittedAt',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      {
        application_submitted_at: {
          order: 'asc',
          format: 'strict_date_optional_time',
          missing: 'CURSOR_DATE_MAX',
        },
      },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by submitter_name when CaseApplicantName is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'ApplicantName',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { 'submitter_name.keyword': { order: 'asc', missing: '_last' } },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by case_status when CaseStatus is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'Status',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { 'case_status.keyword': { order: 'asc', missing: '_last' } },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by application_requested_amount when CaseAwardedAmount is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'AwardedAmount',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { application_requested_amount: { order: 'asc', missing: '_last' } },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by case_program_name when CaseProgramName is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'ProgramName',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { 'case_program_name.keyword': { order: 'asc', missing: '_last' } },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });

  it('should sort by assignee_name when Assignee is specified', () => {
    const repo = new ElasticsearchRepository({
      client: {} as unknown as ElasticsearchClient,
      transformer: {} as unknown as SearchResultTransformer,
    });
    const args = {
      input: {
        index: 'cases_applications',
      },
      sort: {
        column: 'Assignee',
        direction: 'Ascending',
      },
    } as SearchArguments<'cases_applications'>;

    // biome-ignore lint/complexity/useLiteralKeys: private property
    const sort = repo['sortBuilder'](args);

    expect(sort).toEqual([
      { 'assignee_name.keyword': { order: 'asc', missing: '_last' } },
      { 'case_priority.keyword': { order: 'asc' } },
      { 'case_display_id.keyword': { order: 'asc' } },
    ]);
  });
});

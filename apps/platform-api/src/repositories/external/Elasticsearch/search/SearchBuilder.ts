import { Index, SearchArguments } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';

/**
 * Abstract base class for building Elasticsearch search configurations for specific indices.
 * Implements the Strategy pattern to handle different searching logic per index type.
 */
export abstract class SearchBuilder<I extends Index> {
  /**
   * Applies search configuration to the provided search array based on the index-specific logic.
   *
   * @param must - Array to accumulate search query containers
   * @param args - Search arguments
   */
  abstract applySearch(must: estypes.QueryDslQueryContainer[], args: SearchArguments<I>): void;

  /**
   * Returns the index type this search builder handles.
   */
  abstract getIndex(): I;
}

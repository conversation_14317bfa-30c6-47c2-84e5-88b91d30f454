import { Index, SearchArguments } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { logger } from '../../../../utils/logger.js';
import { CasesApplicationsSearchBuilder } from './CasesApplicationsSearchBuilder.js';
import { SearchBuilder } from './SearchBuilder.js';

const builders = new Map<Index, SearchBuilder<Index>>();

/**
 * Registers all available search builders.
 * Add new search builders here as they are created.
 */
function initializeBuilders(): void {
  builders.set('cases_applications', new CasesApplicationsSearchBuilder());
  // Add other search builders here as they are implemented
  // builders.set('other_index', new OtherIndexSearchBuilder());
}

/**
 * Gets the appropriate search builder for the specified index.
 *
 * @param index - The Elasticsearch index to get a search builder for
 * @returns SearchBuilder instance for the specified index
 * @throws Error if no search builder is registered for the index
 */
export function getSearchBuilder<I extends Index>(index: I): SearchBuilder<I> {
  // Initialize builders lazily
  if (builders.size === 0) {
    initializeBuilders();
  }

  const builder = builders.get(index);
  if (!builder) {
    logger.warn(`No search builder registered for index: ${index}. Using generic search builder.`);
    // Fall back to a generic builder that performs a simple query_string search,
    // preserving the previous behaviour and avoiding runtime errors.
    return {
      applySearch: (must: estypes.QueryDslQueryContainer[], args: SearchArguments<I>) => {
        if (args.input.search) {
          must.push({ bool: { should: [{ query_string: { query: `*${args.input.search}*` } }] } });
        }
      },
      getIndex: () => index,
    } as unknown as SearchBuilder<I>;
  }

  return builder as SearchBuilder<I>;
}

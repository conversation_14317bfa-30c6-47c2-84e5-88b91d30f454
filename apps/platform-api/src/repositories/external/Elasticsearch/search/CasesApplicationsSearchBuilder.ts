import { IndexToSearchCategory, SearchArguments } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { SearchBuilder } from './SearchBuilder.js';

export class CasesApplicationsSearchBuilder extends SearchBuilder<'cases_applications'> {
  public applySearch(
    must: estypes.QueryDslQueryContainer[],
    args: SearchArguments<'cases_applications'>,
  ): void {
    if (!args.input.search) return;

    const { search, searchCategories, deepSearch } = args.input;
    const should: estypes.QueryDslQueryContainer[] = [];
    if (searchCategories?.length) {
      this.applyCategorySearch(must, search, searchCategories);
    } else {
      this.applyGlobalSearch(should, search, deepSearch);
    }

    if (should.length > 0) {
      must.push({ bool: { should } });
    }
  }

  private applyGlobalSearch(
    should: estypes.QueryDslQueryContainer[],
    searchText: string,
    deepSearch?: boolean,
  ): void {
    // Base wildcard search across all fields
    should.push({ query_string: { query: `*${searchText}*` } });

    // Application answers search
    if (deepSearch) {
      should.push(this.buildDeepSearch(searchText));
    } else {
      should.push(this.buildNgramSearch(searchText));
    }
  }

  private applyCategorySearch(
    must: estypes.QueryDslQueryContainer[],
    searchText: string,
    categories: IndexToSearchCategory['cases_applications'][],
  ): void {
    for (const category of categories) {
      switch (category) {
        case 'ApplicationId':
          must.push({ match: { application_display_id: searchText } });
          break;
        case 'CaseId':
          must.push({ match: { case_display_id: searchText } });
          break;
        case 'SubmitterName':
          must.push({
            fuzzy: { submitter_name: { value: searchText, fuzziness: 'AUTO' } },
          });
          break;
        case 'SubmitterEmail':
          must.push({
            fuzzy: { submitter_email: { value: searchText, fuzziness: 'AUTO' } },
          });
          break;
        case 'SubmitterPhone':
          must.push({
            fuzzy: { submitter_phone: { value: searchText, fuzziness: 'AUTO' } },
          });
          break;
        case 'SubmitterDisplayId':
          must.push({ match: { submitter_display_id: searchText } });
          break;
        case 'ProgramName':
          must.push({
            fuzzy: { case_program_name: { value: searchText, fuzziness: 'AUTO' } },
          });
          break;
      }
    }
  }

  private buildDeepSearch(searchText: string): estypes.QueryDslQueryContainer {
    // Deep search is a slow and costly operation for the cluster, so it is not enabled by default.
    const wildcardQuery = `*${searchText}*`;
    return {
      nested: {
        path: 'application_answers',
        query: {
          bool: {
            should: [{ wildcard: { 'application_answers.value': wildcardQuery } }],
          },
        },
      },
    };
  }

  private buildNgramSearch(searchText: string): estypes.QueryDslQueryContainer {
    // Use edge ngram field for faster search, though less accurate as it only matches the beginning
    return {
      nested: {
        path: 'application_answers',
        query: { match: { 'application_answers.value.edge': searchText.toLowerCase() } },
      },
    };
  }

  public getIndex(): 'cases_applications' {
    return 'cases_applications';
  }
}

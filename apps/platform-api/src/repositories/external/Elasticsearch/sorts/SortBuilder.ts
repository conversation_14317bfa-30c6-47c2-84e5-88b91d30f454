import { Index, IndexToSortColumn } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';

/**
 * Abstract base class for building Elasticsearch sort configurations for specific indices.
 * Implements the Strategy pattern to handle different sorting logic per index type.
 */
export abstract class SortBuilder<I extends Index> {
  /**
   * Applies sort configuration to the provided sort array based on the index-specific logic.
   *
   * @param sort - Array to accumulate sort configurations
   * @param column - The column to sort by for this specific index
   * @param order - Sort order ('asc' or 'desc')
   */
  abstract applySort(
    sort: estypes.SortCombinations[],
    column: IndexToSortColumn[I],
    order: 'asc' | 'desc',
  ): void;

  /**
   * Applies default sorting for the index when no explicit sort is provided.
   * Each index defines its own default sort order and fields.
   *
   * @param sort - Array to accumulate default sort configurations
   * @param reverse - Whether to reverse the sort order (for backward pagination)
   */
  abstract applyDefaultSort(sort: estypes.SortCombinations[], reverse?: boolean): void;

  /**
   * Returns the index type this sort builder handles.
   */
  abstract getIndex(): I;
}

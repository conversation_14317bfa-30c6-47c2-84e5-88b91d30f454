import { IndexToSortColumn } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { logger } from '../../../../utils/logger.js';
import { DATE_MAX, DATE_MIN } from '../utils/cursor.js';
import { SortBuilder } from './SortBuilder.js';

export class CasesApplicationsSortBuilder extends SortBuilder<'cases_applications'> {
  public applySort(
    sort: estypes.SortCombinations[],
    column: IndexToSortColumn['cases_applications'],
    order: 'asc' | 'desc',
  ): void {
    const baseSort = { order, missing: '_last' };
    const dateSort = {
      order,
      format: 'strict_date_optional_time',
      missing: order === 'desc' ? DATE_MIN : DATE_MAX,
    };

    switch (column) {
      case 'CreatedAt':
        sort.push({ case_created_at: dateSort });
        break;
      case 'UpdatedAt':
        sort.push({ case_status_updated_at: dateSort });
        break;
      case 'SubmittedAt':
        sort.push({ application_submitted_at: dateSort });
        break;
      case 'ApplicantName':
        sort.push({ 'submitter_name.keyword': baseSort });
        break;
      case 'Assignee':
        sort.push({ 'assignee_name.keyword': baseSort });
        break;
      case 'Status':
        sort.push({ 'case_status.keyword': baseSort });
        break;
      case 'AwardedAmount':
        sort.push({ application_requested_amount: baseSort });
        break;
      case 'ProgramName':
        sort.push({ 'case_program_name.keyword': baseSort });
        break;
      default:
        logger.warn({ column }, 'Unknown sort column for cases_applications');
        break;
    }
  }

  public applyDefaultSort(sort: estypes.SortCombinations[], reverse?: boolean): void {
    const defaultOrder = reverse ? 'desc' : 'asc';
    sort.push({ 'case_priority.keyword': { order: defaultOrder } });
    sort.push({ 'case_display_id.keyword': { order: defaultOrder } });
  }

  public getIndex(): 'cases_applications' {
    return 'cases_applications';
  }
}

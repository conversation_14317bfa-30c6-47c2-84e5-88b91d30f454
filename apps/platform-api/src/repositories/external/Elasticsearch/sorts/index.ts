import { Index } from '@bybeam/platform-types';
import { estypes } from '@elastic/elasticsearch';
import { logger } from '../../../../utils/logger.js';
import { CasesApplicationsSortBuilder } from './CasesApplicationsSortBuilder.js';
import { SortBuilder } from './SortBuilder.js';

const builders = new Map<Index, SortBuilder<Index>>();

/**
 * Registers all available sort builders.
 * Add new sort builders here as they are created.
 */
function initializeBuilders(): void {
  builders.set('cases_applications', new CasesApplicationsSortBuilder());
  // Add other sort builders here as they are implemented
  // builders.set('other_index', new OtherIndexSortBuilder());
}

/**
 * Gets the appropriate sort builder for the specified index.
 *
 * @param index - The Elasticsearch index to get a sort builder for
 * @returns SortBuilder instance for the specified index
 * @throws Error if no sort builder is registered for the index
 */
export function getSortBuilder<I extends Index>(index: I): SortBuilder<I> {
  // Initialize builders lazily
  if (builders.size === 0) {
    initializeBuilders();
  }

  const builder = builders.get(index);
  if (!builder) {
    logger.warn(`No sort builder registered for index: ${index}. Using generic sort builder.`);
    // Fall back to using _id as default sort
    return {
      applyDefaultSort(sort: estypes.SortCombinations[], reverse?: boolean): void {
        sort.push({ _id: { order: reverse ? 'desc' : 'asc' } });
      },
      applySort: () => {},
      getIndex: () => index,
    } as unknown as SortBuilder<I>;
  }

  return builder as SortBuilder<I>;
}

import { useQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, PaymentMethod, ScheduleType, User } from '@bybeam/platform-types';
import FieldDetailList from '@platform-ui-common/components/FieldDetail/FieldDetailList.js';
import FieldDetail from '@platform-ui-common/components/FieldDetail/index.js';
import useFulfillment from '@platform-ui-common/hooks/contexts/useFulfillment.js';
import useProgram from '@platform-ui-common/hooks/contexts/useProgram.js';
import {
  DateFormat,
  formatAddress,
  formatCurrency,
  formatDate,
} from '@platform-ui-common/utilities/format.js';
import {
  AccountTypesDisplay,
  PaymentMethodDisplay,
  PaymentScheduleDisplay,
  findLatestPayment,
  getPaymentStatus,
} from '@platform-ui-common/utilities/payment.js';
import useAuth from '@platform-ui/auth/hooks/useAuth/index.js';
import GetAuthenticatedPayeeDetailsQuery from '@platform-ui/claim/graphql/GetAuthenticatedPayeeDetails.graphql';
import PanelWrapper from '../../CaseDetails/components/PanelWrapper.js';

const estimatedArrival = {
  [PaymentMethod.DirectDeposit]: '1-3 business days',
  [PaymentMethod.Check]: '7-10 business days',
  [PaymentMethod.PhysicalCard]: '7 business days',
  [PaymentMethod.VirtualCard]: 'Immediately via Akimbo Now Portal',
  [PaymentMethod.Zelle]: 'Receive funds within minutes',
};

function FundingDetails(): JSX.Element {
  const {
    payments,
    payments: [firstPayment],
  } = useFulfillment();
  const program = useProgram();
  const isClaimFunds = checkFeature(program?.features, FeatureName.PaymentsClaimFunds);

  const latestPayment = findLatestPayment(payments);
  const payment = latestPayment ?? firstPayment;

  const { data } = useQuery<{ currentUser: User }>(GetAuthenticatedPayeeDetailsQuery, {
    skip: isClaimFunds,
    variables:
      payment.method === PaymentMethod.DirectDeposit
        ? { includeBankAccount: true }
        : { includeMailingAddress: true },
  });

  switch (payment.method) {
    case PaymentMethod.DirectDeposit:
      if (isClaimFunds) return <></>;
      return (
        <>
          <FieldDetail label="ACCOUNT TYPE">
            {AccountTypesDisplay[data?.currentUser.bankAccount?.accountType] ?? 'N/A'}
          </FieldDetail>
          <FieldDetail label="ACCOUNT NUMBER">
            {data?.currentUser.bankAccount?.accountNumber ?? 'N/A'}
          </FieldDetail>
        </>
      );
    case PaymentMethod.PhysicalCard:
    case PaymentMethod.VirtualCard:
      return (
        <>
          <FieldDetail label="EMAIL ADDRESS">{payment.payee.email}</FieldDetail>
          {!isClaimFunds && (
            <FieldDetail label="MAILING ADDRESS">
              {formatAddress(data?.currentUser.mailingAddress)}
            </FieldDetail>
          )}
        </>
      );
    default:
      return <></>;
  }
}

export default function PaymentDetailsPanel(): JSX.Element {
  const { user } = useAuth();
  const program = useProgram();
  const fulfillment = useFulfillment();
  const {
    payments,
    payments: [firstPayment],
    paymentPattern,
    scheduleType,
  } = fulfillment;
  const latestPayment = findLatestPayment(payments);
  const payment = latestPayment ?? firstPayment;

  return (
    <PanelWrapper title="Payment Details">
      <FieldDetailList columns={1}>
        <FieldDetail label="AMOUNT">{formatCurrency(payment.amount, true)}</FieldDetail>
        <FieldDetail label="PAYMENT METHOD">
          {PaymentMethodDisplay[payment.method]}
          {scheduleType === ScheduleType.Recurring &&
            ` - ${PaymentScheduleDisplay[paymentPattern.pattern]}`}
        </FieldDetail>
        <FieldDetail label="PAYMENT STATUS">
          {getPaymentStatus(program, fulfillment) ?? '-'}
        </FieldDetail>
        {payment.payee.id !== user?.core?.id && (
          <FieldDetail label="PAID TO">{payment.payee.name}</FieldDetail>
        )}
        {payment.method === PaymentMethod.Check && payment.mailingAddress && (
          <FieldDetail label="MAILING ADDRESS">{formatAddress(payment.mailingAddress)}</FieldDetail>
        )}
        {scheduleType === ScheduleType.Recurring && (
          <FieldDetail label="NUMBER OF PAYMENTS">{paymentPattern.count ?? 0} payments</FieldDetail>
        )}
        {payment.payee.id === user?.core?.id && <FundingDetails />}
        {scheduleType === ScheduleType.OneTime && (
          <>
            <FieldDetail label="PAYMENT DATE">
              {formatDate(payment.initiatedAt, DateFormat.DateTime)}
            </FieldDetail>
            <FieldDetail label="ESTIMATED ARRIVAL">{estimatedArrival[payment.method]}</FieldDetail>
          </>
        )}
      </FieldDetailList>
    </PanelWrapper>
  );
}

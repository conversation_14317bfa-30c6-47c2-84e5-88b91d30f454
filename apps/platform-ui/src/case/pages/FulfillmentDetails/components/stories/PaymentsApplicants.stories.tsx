import {
  Application,
  CaseStatus,
  FeatureName,
  Fulfillment,
  MailingAddressType,
  Payee,
  PayeeType,
  Payment,
  PaymentMethod,
  PaymentStatus,
  Program,
  ScheduleType,
} from '@bybeam/platform-types';
import { FulfillmentContext } from '@platform-ui-common/hooks/contexts/useFulfillment.js';
import MockedRouter from '@platform-ui-test/components/MockedRouter.js';
import { PARTNER, PROGRAM, USER, enabledFeature } from '@platform-ui-test/mocks/mockData.js';
import { CaseContext } from '@platform-ui/case/hooks/useCase.js';
import { Meta, StoryObj } from '@storybook/react';
import dayjs from 'dayjs';
import FulfillmentDetails from '../FulfillmentDetails.js';
import { payeeMock } from './mocks.js';

const meta: Meta<typeof FulfillmentDetails> = {
  title: 'Fulfillment Details/Payments Applicants',
  component: FulfillmentDetails,
  decorators: [
    (Story) => (
      <MockedRouter>
        <Story />
      </MockedRouter>
    ),
  ],
  render: ({ case: case_ }) => {
    return (
      <CaseContext.Provider value={{ case_ }}>
        <FulfillmentContext.Provider value={case_?.fulfillments?.[0]}>
          <FulfillmentDetails />
        </FulfillmentContext.Provider>
      </CaseContext.Provider>
    );
  },
};
export default meta;

type Story = StoryObj<typeof FulfillmentDetails>;

function story(method: PaymentMethod): Story {
  const commonDate = '06/26/2015';
  const program = {
    ...PROGRAM,
    features: [
      enabledFeature(FeatureName.PaymentsPartnerIssued),
      enabledFeature(FeatureName.PaymentsApplicants),
    ],
  } as Program;

  return {
    parameters: {
      apolloClient: { mocks: [payeeMock(method)] },
      application: {
        applications: [
          {
            id: 'eacb886-0f7d7-47db-9971-08545b93bc22',
            displayId: 'A000000253',
            submittedAt: dayjs(commonDate).toDate(),
            submitter: {
              displayId: 'A000000253',
              name: 'Alexanne Ramos',
              phone: '**********',
              email: '<EMAIL>',
            },
            case: { id: 'mockCaseId' },
          } as Application,
        ],
        partner: { ...PARTNER, programs: [program] },
        program,
      },
    },
    args: {
      case: {
        id: 'mockCaseId',
        displayId: 'C000009999',
        name: 'Alexanne Ramos',
        status: CaseStatus.PaymentSent,
        decisionReachedAt: commonDate,
        fulfillments: [
          {
            displayId: 'F000123456',
            approvedAmount: 50000,
            scheduleType: ScheduleType.OneTime,
            payments: [
              {
                id: '123456',
                payeeType: PayeeType.User,
                method,
                amount: 50000,
                initiatedAt: commonDate,
                status: PaymentStatus.Success,
                payee: {
                  id: USER.id,
                  name: `${PayeeType.User} Payee`,
                  email: '<EMAIL>',
                } as unknown as Payee,
                ...(method === PaymentMethod.Check && {
                  mailingAddressType: MailingAddressType.Payee,
                  mailingAddress: {
                    addressLine1: '1 Main St',
                    city: 'Hanover',
                    state: 'NH',
                    zip: '03755',
                  },
                }),
              } as unknown as Payment,
            ],
          } as unknown as Fulfillment,
        ],
      },
    },
  };
}

// Payments Applicants Case Details
export const DirectDeposit: Story = story(PaymentMethod.DirectDeposit);
export const PaperCheck: Story = story(PaymentMethod.Check);
export const PhysicalCard: Story = story(PaymentMethod.PhysicalCard);
export const VirtualCard: Story = story(PaymentMethod.VirtualCard);
export const OtherParty: Story = story(PaymentMethod.DirectDeposit);
OtherParty.parameters.application.user = {
  ...USER,
  id: 'other-party-user-id',
};

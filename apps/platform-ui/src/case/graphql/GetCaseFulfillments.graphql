#import "../../graphql/fragments/PaymentPatternFragment.graphql"

query GetCaseFulfillments($id: UUID!) {
  cases(filter: {ids: [$id]})  {
    cases {
        id
        displayId
        status
        fulfillments {
          id
          displayId
          approvedAmount
          scheduleType
          ...PaymentPatternFragment
          payments {
            id
            displayId
            status
            createdAt
            initiatedAt
            scheduledFor
            method
            status
            amount
            payeeType
            payee {
              __typename
              ... on Vendor {
                id
                name
                email
              }
              ... on User {
                id
                name
                email
              }
            }
            mailingAddressType
            mailingAddress {
              addressLine1
              addressLine2
              city
              state
              zip
            }
          }
        }
        program {
          id
        }
      }
  }
}

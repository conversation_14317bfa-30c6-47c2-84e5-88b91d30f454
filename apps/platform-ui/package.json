{"name": "@bybeam/platform-ui", "version": "0.0.1", "private": true, "type": "module", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "scripts": {"build:prod": "NODE_OPTIONS='--max-old-space-size=4096' vite build", "build:preview": "vite build && vite preview", "clean": "rm -rf build", "start": "vite serve", "start:debug": "vite serve --debug --log-level info", "start:dev": "vite serve", "start:force": "vite serve --force", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "chromatic": "chromatic --build-script-name storybook:build", "storybook": "NODE_OPTIONS=--openssl-legacy-provider && storybook dev -p 6006", "storybook:build": "NODE_OPTIONS='--max-old-space-size=4096 --openssl-legacy-provider' storybook build", "storybook:force": "rm -rf ./storybook-static && NODE_OPTIONS=--openssl-legacy-provider && storybook dev -p 6006 --no-manager-cache"}, "dependencies": {"@apollo/client": "3.9.4", "@bybeam/doctopus-types": "workspace:*", "@bybeam/formatting": "workspace:^", "@bybeam/identity-client": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/verification-types": "workspace:^", "@datadog/browser-logs": "5.4.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@grafana/faro-react": "1.18.2", "@grafana/faro-web-tracing": "1.18.2", "@mui/icons-material": "6.1.1", "@mui/lab": "5.0.0-alpha.71", "@mui/material": "5.15.16", "@mui/styles": "5.15.16", "@radix-ui/react-popover": "1.0.7", "@radix-ui/themes": "^3.2.1", "@react-input/mask": "1.0.20", "@react-pdf/renderer": "3.1.14", "@sentry/integrations": "7.110.0", "@sentry/react": "7.110.0", "@sentry/vite-plugin": "2.16.1", "@tanstack/react-table": "8.10.7", "apollo-upload-client": "18.0.1", "classnames": "2.5.1", "core-js": "3.35.1", "dayjs": "1.11.11", "firebase": "^10.8.0", "flat": "5.0.2", "graphql": "16.9.0", "html-react-parser": "^5.1.8", "imagekitio-react": "4.3.0", "isemail": "3.2.0", "lodash": "4.17.21", "posthog-js": "1.215.5", "react": "18.2.0", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-helmet": "6.1.0", "react-idle-timer": "^5.7.2", "react-markdown": "8.0.7", "react-modal": "3.16.1", "react-month-picker": "2.2.1", "react-router": "5.3.4", "react-router-dom": "5.3.4", "react-router-dom-v5-compat": "6.22.0", "react-string-replace": "1.1.1", "react-use-error-boundary": "3.0.0", "regenerator-runtime": "0.14.1", "sanitize-filename": "^1.6.3", "sanitize-html": "^2.12.1", "uuid": "9.0.1"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "0.2.3", "@esbuild-plugins/node-modules-polyfill": "0.2.2", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-graphql": "2.0.4", "@rollup/plugin-inject": "5.0.5", "@storybook/addon-a11y": "^8.0.9", "@storybook/addon-docs": "^8.0.9", "@storybook/addon-essentials": "^8.0.9", "@storybook/blocks": "^8.0.9", "@storybook/react": "^8.0.9", "@storybook/react-vite": "^8.0.9", "@testing-library/jest-dom": "6.4.8", "@testing-library/react": "14.2.2", "@types/flat": "5.0.5", "@types/node": "22.0.0", "@types/react": "18.2.67", "@types/react-dom": "18.3.1", "@types/react-router-dom": "5.3.3", "@types/react-table": "7.7.19", "@types/sanitize-html": "^2.11.0", "@vitejs/plugin-react": "4.2.1", "@vitest/coverage-v8": "2.1.9", "autoprefixer": "10.4.16", "chromatic": "11.18.1", "graphql-tag": "2.12.6", "jsdom": "^25.0.1", "mockdate": "3.0.5", "postcss": "8.4.49", "postcss-flexbugs-fixes": "5.0.2", "postcss-import": "15.1.0", "postcss-preset-env": "10.0.0", "storybook": "^8.0.9", "storybook-addon-apollo-client": "7.3.0", "storybook-mock-date-decorator": "2.0.6", "tailwindcss": "3.4.0", "typescript": "5.5.4", "vite": "5.4.19", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}
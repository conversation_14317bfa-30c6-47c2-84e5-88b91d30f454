# PubSub to Elasticsearch Synchronizer

A high-performance data synchronization service that efficiently streams data from Google Cloud PubSub to Elasticsearch.

## Features

- **High-throughput**: Optimized for processing large volumes of messages
- **Concurrent processing**: Multi-threaded architecture for maximum performance
- **Adaptive batching**: Dynamically adjusts batch sizes based on message size and queue state
- **Dynamic configuration**: Subscription-specific configurations for ID fields, document templates, and queries
- **Robust error handling**: Distinguishes between different error types for better monitoring
- **Performance monitoring**: Detailed metrics on throughput, processing rates, and system health
- **Backpressure handling**: Automatically manages flow control when system is under load
- **Optimized JSON processing**: Uses orjson/ujson for faster JSON parsing and serialization
- **Memory-efficient**: Pre-allocated arrays and optimized data structures reduce memory usage
- **Parallel bulk operations**: Processes large batches in parallel for better throughput
- **Reduced lock contention**: Minimizes time spent in critical sections for better concurrency
- **PostgreSQL connection pooling**: Reuses database connections for better performance
- **Automatic data type conversion**: Handles Decimal values by converting them to float for JSON serialization
- **Exponential backoff retry**: Automatically retries failed operations with increasing delays
- **Elasticsearch schema compatibility**: Transforms data to match Elasticsearch's expected format
- **Nested field handling**: Properly handles nested objects in Elasticsearch mappings
- **Field limiting**: Intelligently limits fields to prevent exceeding Elasticsearch's field limit
- **Historical rate tracking**: Tracks processing rates over time for trend analysis

## Architecture

The Elasticsearch synchronization service uses a flexible, configuration-driven architecture that allows for dynamic handling of different data types and subscription patterns.

### Key Components

1. **Subscription Manager**: Manages the complete pipeline for a single subscription
   - Configurable with subscription-specific settings
   - Handles message reception, processing, and delivery

2. **Raw Processor**: Processes raw messages from PubSub
   - Extracts ID fields based on subscription configuration
   - Supports dynamic ID field extraction using configurable field lists

3. **Batch Processor**: Fetches data from PostgreSQL
   - Uses dynamic SQL queries based on subscription type
   - Supports variable ID fields for different entity types
   - Efficiently batches database queries for optimal performance

4. **Message Processor**: Transforms messages into Elasticsearch actions
   - Generates document IDs using configurable templates
   - Supports different operation types (insert, update, delete)
   - Optimizes message transformation for bulk operations

5. **Worker Manager**: Manages worker threads for parallel processing
   - Distributes batches to worker threads
   - Monitors worker performance and health

### Configuration-Driven Design

The service uses a configuration-driven approach that allows for:

- **Dynamic ID Fields**: Each subscription can define its own set of ID fields
- **Custom Document ID Templates**: Flexible document ID generation using templates
- **Subscription-Specific Queries**: Different database queries for different entity types
- **Operation Type Defaults**: Default operation types per subscription

Example configuration:
```json
{
  "application-answers-updated": {
    "es_index": "cases_applications",
    "operation_type": "update",
    "query": "cases_applications",
    "id_fields": ["case_id", "application_id"],
    "doc_id_template": "{case_id}|{application_id}"
  },
  "partner-updated": {
    "es_index": "partners",
    "operation_type": "update",
    "query": "partners",
    "id_fields": ["partner_id"],
    "doc_id_template": "{partner_id}"
  }
}
```

### Data Flow

1. **Message Reception**:
   - PubSub messages are received by the Subscription Manager
   - Messages are added to the Raw Accumulator

2. **Raw Processing**:
   - Raw Processor extracts ID fields based on subscription config
   - Messages with all required ID fields are sent to Batch Processor
   - Messages with pre-defined `_id` bypass Batch Processor

3. **Batch Processing**:
   - Batch Processor collects ID values for each configured field
   - Dynamic SQL query is executed with ID arrays as parameters
   - Query results are processed and enriched with metadata

4. **Message Accumulation**:
   - Processed messages are added to Message Accumulator
   - Message Accumulator batches messages for efficient processing

5. **Worker Processing**:
   - Worker Manager assigns batches to worker threads
   - Message Processor transforms messages to Elasticsearch actions
   - Elasticsearch bulk API is used for efficient indexing

## Installation

### Prerequisites

- Python 3.11 or higher
- Access to Google Cloud PubSub and Elasticsearch
- GCP service account with appropriate permissions

### Setup

1. Clone the repository:
   ```
   gh repo clone edquity/data-core
   ```

2. Install dependencies using Poetry:
  ```
  # Install Poetry if you don't have it
  pip install poetry

  # Regenerate the lock file with proper metadata
  poetry lock

  # Install dependencies
  poetry install

  # For development only (includes dev dependencies)
  poetry install --with dev
  ```

3. Set up GCP authentication:
  ```
   # authenticate using gcloud
   gcloud auth application-default login
   ```

## Configuration

Create a `.env` file with the following parameters:

```
PROJECT_ID=your-gcp-project-id
SUBSCRIPTION_ID=test_sync-sub
PUBSUB_TOPIC=test_sync
ES_INDEX=cases_applications_test

# Queue and batch settings
CURRENT_QUEUE_SIZE=1000000
BATCH_SIZE=2500
BATCH_WAIT=0.10
RAW_BATCH_SIZE=500
RAW_BATCH_WAIT=0.10
RAW_QUEUE_SIZE=100000
WORKER_THREADS=32

# PubSub settings
MAX_PUBSUB_MESSAGES=20000
MAX_PUBSUB_BYTES=********
PUBSUB_STREAMS=10

# Connection settings
SECRET_TIMEOUT=30
USE_MSGPACK=true
SKIP_ES=false
CONTINUE_WITHOUT_ES=true
USE_LOCAL_ES=false
# ES_HOST=**********  # Docker bridge network gateway IP, uncomment when using bridge network with local ES

# Retry settings
DB_RETRY_MAX_MINUTES=10
ES_RETRY_MAX_MINUTES=10

# API settings
METRICS_PORT=8080

# Database connection settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your-password  # Replace with your actual password
# PG_PASSWORD_SECRET_ID=postgres-password  # Use either DB_PASSWORD or PG_PASSWORD_SECRET_ID, not both
DB_MIN_CONNECTIONS=1
DB_MAX_CONNECTIONS=10

# Elasticsearch secret IDs
ES_CLOUD_ID_SECRET_ID=elastic-beam-prod-us-central1-cpu-optimized-cloud_id
ES_API_KEY_SECRET_ID=elastic-beam-prod-us-central1-cpu-optimized-admin-superuser-api-key
```

### Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `project_id` | GCP project ID | (Required) |
| `subscription_id` | PubSub subscription ID | (Required) |
| `es_index` | Elasticsearch index name | (Required) |

**Queue and Batch Settings**

| Parameter | Description | Default |
|-----------|-------------|---------|
| `current_queue_size` | Maximum size of processed message queue | 1000000 |
| `batch_size` | Target size for processed message batches | 5000 |
| `batch_wait` | Maximum time (seconds) to wait before flushing incomplete processed batches | 0.05 |
| `raw_batch_size` | Target size for raw message batches | 1000 |
| `raw_batch_wait` | Maximum time (seconds) to wait before flushing incomplete raw batches | 0.05 |
| `raw_queue_size` | Maximum size of raw message queue | 200000 |
| `worker_threads` | Number of concurrent worker threads | 64 |

**PubSub Settings**

| Parameter | Description | Default |
|-----------|-------------|---------|
| `max_pubsub_messages` | Maximum PubSub messages held in memory | 20000 |
| `max_pubsub_bytes` | Maximum PubSub message bytes held in memory | ******** |
| `pubsub_streams` | Number of concurrent PubSub streams | 10 |

**Connection Settings**

| Parameter | Description | Default |
|-----------|-------------|---------|
| `continue_without_es` | Continue if Elasticsearch connection fails | true |
| `secret_timeout` | Timeout for fetching secrets | 30 |
| `use_msgpack` | Use MessagePack for serialization | true |
| `skip_es` | Skip Elasticsearch operations (for testing) | false |
| `use_local_es` | Use local Elasticsearch instance instead of cloud | false |
| `es_host` | Host for local Elasticsearch (used when `use_local_es=true`) | localhost |

**Retry Settings**

Retry settings are hardcoded in the Elasticsearch client:

- Initial retry interval: 1 second
- Maximum retry interval: 10 minutes (600 seconds)
- Exponential backoff: Doubles the interval after each failed attempt

**API Settings**

| Parameter | Description | Default |
|-----------|-------------|---------|
| `metrics_port` | Port for the metrics API server | 8080 |

**Database Settings**

| Parameter | Description | Default |
|-----------|-------------|--------|
| `db_host` | PostgreSQL host | localhost |
| `db_port` | PostgreSQL port | 5432 |
| `db_name` | PostgreSQL database name | postgres |
| `db_user` | PostgreSQL username | postgres |
| `db_password` | PostgreSQL password (direct) | (None) |
| `pg_password_secret_id` | Secret ID for PostgreSQL password | postgres-password |

**Note**: Use either `db_password` or `pg_password_secret_id`, but not both. If both are provided, `db_password` takes precedence.
| `db_min_connections` | Minimum number of database connections | 1 |
| `db_max_connections` | Maximum number of database connections | 10 |

**Secret Manager Settings**

| Parameter | Description | Default |
|-----------|-------------|--------|
| `es_cloud_id_secret_id` | Secret ID for Elasticsearch Cloud ID | elastic-beam-prod-us-central1-cpu-optimized-cloud_id |
| `es_api_key_secret_id` | Secret ID for Elasticsearch API Key | elastic-beam-prod-us-central1-cpu-optimized-admin-superuser-api-key |

## Usage

### Starting the Service

```
# Using default .env file in current directory
poetry run python main.py
```

### Docker Deployment

Before building the Docker image, make sure to generate a proper lock file:

```bash
# Regenerate the lock file with proper metadata
poetry lock
```

The service can then be deployed as a Docker container:

```bash
# Build the Docker image
docker build -t es-sync .

# Option 1: Run with bridge network (recommended for accessing metrics API)
docker run -p 8080:8080 \
  --env-file .env \
  --cpus=4 \
  --memory=4g \
  -v ~/.config/gcloud:/root/.config/gcloud \
  es-sync

# Option 2: Run with host network (if you need to access local services)
docker run \
  --network=host \
  --env-file .env \
  --cpus=4 \
  --memory=4g \
  -v ~/.config/gcloud:/root/.config/gcloud \
  es-sync
```

Option 1 (bridge network) setup:
- Maps port 8080 from the container to port 8080 on your host machine (for the metrics API)
- Uses the environment variables from your `.env` file
- Mounts your local GCP credentials for authentication with Google Cloud services
- **Recommended when you need to access the metrics API**

Option 2 (host network) setup:
- Uses `--network=host` to allow the container to access local services (like PostgreSQL or Elasticsearch)
- Uses the environment variables from your `.env` file
- Mounts your local GCP credentials for authentication with Google Cloud services
- **Note**: With host networking, the metrics API may not be accessible from the host machine

The Dockerfile is configured to use Python 3.11 and exposes port 8080 for Cloud Run compatibility.

#### Accessing Metrics from Docker

Once the container is running, you can access the metrics API at:

```
http://localhost:8080/metrics
```

You can also use the test script to monitor the metrics:

```bash
# Run the test script to monitor metrics
poetry run python test_metrics_api.py http://localhost:8080 --monitor
```

### Secure Credentials

The service uses Google Cloud Secret Manager for Elasticsearch and PostgreSQL credentials. Ensure the following secrets are set up:

- Elasticsearch Cloud ID (configurable via `ES_CLOUD_ID_SECRET_ID` environment variable)
- Elasticsearch API key (configurable via `ES_API_KEY_SECRET_ID` environment variable)
- PostgreSQL password (configurable via `PG_PASSWORD_SECRET_ID` environment variable if not using direct `DB_PASSWORD`)

You can customize the secret IDs by setting the appropriate environment variables in your `.env` file.

**Note**: For PostgreSQL authentication, you should use either `DB_PASSWORD` (direct password in environment) or `PG_PASSWORD_SECRET_ID` (Secret Manager), but not both. If both are provided, `DB_PASSWORD` takes precedence.

## System Architecture

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  PubSub Service  +---->+  ES Sync Service +---->+   Elasticsearch  |
|                  |     |                  |     |                  |
+------------------+     +-------+----------+     +------------------+
                                 |
                                 v
                         +-------+----------+
                         |                  |
                         |   PostgreSQL     |
                         |                  |
                         +------------------+
                                 |
                                 v
                         +-------+----------+
                         |                  |
                         |   Metrics API    |
                         |                  |
                         +------------------+
```

The service consists of the following main components:

```
es_sync/
├── main.py                              # Entry point
├── Dockerfile                           # Docker configuration
├── elasticsearch.Dockerfile             # Elasticsearch Docker configuration
├── docker-entrypoint-with-setup.sh      # Docker entrypoint script
├── run_elasticsearch.sh                 # Script to run Elasticsearch locally
├── setup_local_elasticsearch.py         # Script to set up local Elasticsearch
├── LOCAL_ELASTICSEARCH.md               # Documentation for local Elasticsearch setup
├── .env                                 # Environment variables (not in repo)
├── .env.example                         # Sample environment variables
├── pyproject.toml                       # Modern Python package configuration
├── test_metrics_api.py                  # Script to test metrics API
├── docs/
│   ├── architecture.md                  # Detailed architecture documentation
├── core/
│   ├── accumulator.py                   # Processed message batching
│   ├── raw_accumulator.py               # Raw message batching
│   ├── elasticsearch_client.py          # ES connection handling
│   ├── elasticsearch_synchronizer.py    # Main orchestrator
│   ├── message_processor.py             # Transform & process messages
│   ├── performance_monitor.py           # Metrics & logging
│   ├── pubsub_subscriber.py             # PubSub subscription
│   ├── batch_processor.py               # Elasticsearch batch processing
│   ├── query_utils.py                   # Optimized PostgreSQL query
│   ├── pg_connection_pool.py            # PostgreSQL connection pooling
│   ├── worker_manager.py                # Worker thread coordination
│   └── subscription_manager.py          # Subscription-specific processing
├── api/
│   └── metrics_api.py                   # Metrics API endpoints
├── utils/
│   ├── json_parsing.py                  # JSON parsing utilities
│   ├── logging_config.py                # Logging configuration
│   ├── logging_setup.py                 # Logging setup
│   ├── env_utils.py                     # Environment variable utilities
│   └── subscription_config.py           # Subscription configuration
├── schemas/
│   └── cases_programs_applications_schema_template.json  # ES index template
├── elasticsearch/
│   └── elasticsearch.yml                # Elasticsearch configuration
└── tests/
    ├── conftest.py                      # Test fixtures
    ├── test_batch_processor.py          # Batch processor tests
    ├── test_elasticsearch_client_basic.py # ES client tests
    ├── test_main_basic.py               # Main module tests
    ├── test_message_accumulator.py      # Message accumulator tests
    ├── test_message_processor_basic.py  # Message processor tests
    ├── test_metrics_api_basic.py        # Metrics API tests
    ├── test_performance.py              # Performance tests
    ├── test_performance_monitor.py      # Performance monitor tests
    ├── test_pubsub_subscriber.py        # PubSub subscriber tests
    ├── test_query_utils.py              # Query utils tests
    ├── test_subscription_manager.py     # Subscription manager tests
    └── test_worker_manager.py           # Worker manager tests
```

### Data Flow

The system processes data through the following flow:

1. **Message Reception**:
   - PubSub messages are received by the `PubSubSubscriber`
   - Messages are passed to the `RawMessageAccumulator` for batching
   - If the raw message queue fills up, PubSub polling is paused to provide backpressure

2. **Raw Message Processing**:
   - Batches of raw messages are processed by the `SubscriptionManager`
   - The manager extracts and validates messages from the PubSub data
   - For messages with required ID fields (configured per subscription), the system fetches complete data from PostgreSQL
   - The `BatchProcessor` handles the synchronization between PostgreSQL and Elasticsearch
   - The `query_utils.py` module provides optimized SQL queries based on subscription type
   - The `pg_connection_pool.py` module manages PostgreSQL connections with pooling and retry logic
   - Valid messages are collected and passed to the next stage

3. **Processed Message Accumulation**:
   - Valid messages are passed to the `MessageAccumulator`
   - Processed messages are collected into batches for efficient Elasticsearch processing
   - When a batch is ready (based on size or time), it's sent to the processing queue

4. **Concurrent Processing**:
   - The `WorkerManager` maintains a pool of worker threads
   - Each worker thread takes a batch from the queue and processes it
   - Processing includes transforming the data and sending it to Elasticsearch

5. **Elasticsearch Indexing**:
   - The `MessageProcessor` transforms messages into Elasticsearch bulk actions
   - Document IDs are generated using subscription-specific templates
   - The `ElasticsearchClient` sends these actions to Elasticsearch in optimized batches
   - If Elasticsearch is unavailable, it will retry with exponential backoff
   - After 5 retries, failed messages are requeued for later processing
   - Results and errors are tracked for monitoring and reporting

6. **Performance Monitoring**:
   - The `PerformanceMonitor` tracks metrics throughout the process
   - Metrics are logged and exposed via the metrics API
   - This includes processing rates, queue fullness, and error counts
   - Historical rates are tracked for trend analysis
   - Accumulator status and flush times are monitored

7. **Metrics API**:
   - The `metrics_api.py` module provides a FastAPI-based metrics endpoint
   - The API exposes metrics at `/metrics` and a health check at `/health`
   - Metrics are formatted as JSON for easy consumption by monitoring tools
   - The API can be accessed at http://localhost:8080 by default

### Subscription Configuration

The system uses a configuration-driven approach to handle different subscription types:

1. **Subscription Config**:
   - Each subscription has its own configuration in `subscription_config.py`
   - Configurations include:
     - `es_index`: Target Elasticsearch index
     - `operation_type`: Default operation type (insert, update, delete)
     - `query`: Name of the query to use for batch processing
     - `id_fields`: List of fields to use for document identification
     - `doc_id_template`: Template for generating document IDs

2. **Dynamic Query Selection**:
   - The `query_utils.py` module contains query templates for different entity types
   - Queries are selected based on the subscription configuration
   - Each query is optimized for its specific entity type and ID fields

3. **Document ID Generation**:
   - Document IDs are generated using the template specified in the subscription config
   - The template can reference any fields available in the message
   - This allows for flexible ID generation strategies for different entity types

### Semaphore Mechanics

The system uses semaphores to control concurrency and prevent resource exhaustion:

1. **Worker Thread Semaphore**:
   - The `WorkerManager` uses a semaphore to limit the number of concurrent worker threads
   - Each worker acquires a semaphore before processing a batch and releases it when done
   - This ensures that only a controlled number of threads are active at any time
   - If all semaphores are in use, new batches wait in the queue until a worker becomes available
   - This prevents overwhelming the system with too many concurrent operations

2. **Elasticsearch Bulk Operation Semaphore**:
   - Large batches of Elasticsearch operations are split into smaller chunks
   - A semaphore controls how many chunks can be processed concurrently
   - This prevents overwhelming Elasticsearch with too many concurrent requests
   - The semaphore is released as soon as a chunk is processed, allowing the next chunk to proceed

These semaphore mechanisms ensure that the system maintains optimal performance even under heavy load by preventing resource contention and controlling the flow of data through the pipeline.


## Performance Tuning

The service includes several optimizations for high-throughput environments:

1. **Adaptive batch sizing**: Automatically adjusts batch sizes based on message size and system load
2. **Connection pooling**: Optimized Elasticsearch client settings for concurrent operations
3. **Parallel processing**: Multi-threaded architecture with semaphore-controlled concurrency
4. **Backpressure handling**: Automatically extends message acknowledgement deadlines when the system is under load
5. **Optimized memory usage**: Efficient data structures and processing algorithms
6. **Multiple subscription streams**: Leverages multiple PubSub streams for higher throughput
7. **Dual accumulators**: Separate accumulators for raw and processed messages provide better flow control and resilience

## Connection Resilience

The service is designed to be resilient to connection issues:

1. **Elasticsearch Connection Retry**:
   - Automatically retries Elasticsearch connections with exponential backoff
   - Starts with 1 second retry interval and doubles after each failure
   - Maximum retry interval is 10 minutes (configurable)
   - Continues processing messages even when Elasticsearch is unavailable
   - Messages are retried 5 times before being requeued

2. **PostgreSQL Connection Pooling and Retry**:
   - Uses a shared PostgreSQL connection instance to reduce connection overhead
   - Automatically retries failed PostgreSQL connections with exponential backoff
   - Handles connection errors gracefully with proper cleanup
   - Converts Decimal values to float for JSON serialization
   - Simplifies database queries for better performance and reliability

3. **PubSub Backpressure**:
   - When the raw message queue fills up (>80% full), PubSub polling is paused
   - Message processing continues during this pause to drain the queue
   - Once the queue has room, polling resumes automatically
   - This prevents memory exhaustion during processing spikes

4. **Dual Queue System**: The system uses two separate queues (raw message queue and processed message queue) to provide better flow control and resilience. See the "Dual Queue System" section below for details.

## Monitoring

The service logs detailed performance metrics every 10 seconds, including:

- Message rates (received, processed, sent)
- Historical rates for trend analysis
- Queue fullness and processing capacity
- Error rates and types
- Processing times and latencies
- PubSub compression ratio (records per message)
- Connection status for Elasticsearch
- Accumulator status and flush times

### Metrics API

The service also exposes a metrics API at http://localhost:8080 that provides the performance metrics in JSON format. The following endpoints are available:

- `/metrics` - Returns the current performance metrics in JSON format
- `/health` - Health check endpoint

The metrics endpoint returns a comprehensive JSON object with the following sections:

- `timestamp` - Current timestamp
- `uptime_seconds` - Service uptime in seconds
- `pubsub` - PubSub message statistics including rates and compression ratio
- `records` - Record processing statistics including current and historical rates
- `errors` - Error counts and rates
- `queue` - Queue fullness and processing capacity
- `timings` - Processing times and latencies
- `accumulator` - Accumulator status and flush times

You can customize the port using the `METRICS_PORT` environment variable:

```
export METRICS_PORT=9090
```

A test script is provided to demonstrate how to use the metrics API:

```
# Test the metrics API
poetry run python test_metrics_api.py

# Test with a custom URL
poetry run python test_metrics_api.py http://localhost:9090

# Continuous monitoring (updates every 5 seconds)
poetry run python test_metrics_api.py http://localhost:8080 --monitor
```

## Testing

The service includes a comprehensive test suite using pytest. The tests are designed to run without any external dependencies, using mocks for services like Elasticsearch and PubSub.

```bash
# Install dependencies including dev dependencies
poetry install --with dev

# Run all tests
poetry run pytest

# Run specific test file
poetry run pytest tests/test_message_processor_basic.py

# Run specific test class
poetry run pytest tests/test_message_processor_basic.py::TestMessageProcessorBasic

# Run specific test method
poetry run pytest tests/test_message_processor_basic.py::TestMessageProcessorBasic::test_transform_messages_insert
```

#### Test Components

- **Message Processor Tests**: Verify message transformation and batch processing
- **Elasticsearch Client Tests**: Test client behavior with different configurations
- **Metrics API Tests**: Validate metrics collection and reporting
- **Main Module Tests**: Ensure proper initialization and configuration
- **Optimization Tests**: Verify performance improvements of optimized components

#### Performance Testing

The optimized components include performance benchmarks that can be run to verify the improvements:

```bash
poetry run pytest tests/test_performance.py -v

# Run specific optimization test
poetry run pytest tests/test_performance.py::TestMessageProcessorPerformance::test_optimized_transform_messages
```

These tests compare the performance of the optimized components against baseline implementations to ensure that the optimizations are effective.
- **Optimization Tests**: Verify performance improvements of optimized components

#### Manual Testing

For manual testing, you can run the service with Elasticsearch operations disabled:

```bash
# Set SKIP_ES=true in your .env file or use environment variable
SKIP_ES=true python main.py
```

## Dual Queue System

The Elasticsearch synchronization service uses a dual queue architecture to provide better flow control, resilience, and performance. This design separates the concerns of message reception from message processing, allowing each stage to operate at its own optimal pace.

### Why Two Queues Are Necessary

1. **Different Processing Stages**: The system has two distinct processing stages with different characteristics:
   - **Raw Message Reception**: Receiving and parsing PubSub messages, which are typically small and arrive at variable rates
   - **Processed Message Handling**: Processing complete records with data from PostgreSQL, which are larger and require more resources

2. **Decoupling for Resilience**:
   - If Elasticsearch becomes unavailable, the processed message queue can fill up while the system retries
   - With separate queues, the raw message reception can continue independently, storing messages until processing resumes
   - This prevents message loss during temporary outages

3. **Optimized Batching**:
   - Raw messages can be batched for efficient database lookups
   - Processed messages can be batched for optimal Elasticsearch bulk operations
   - Each queue can use different batch sizes and timing strategies

4. **Independent Flow Control**:
   - The raw message queue provides backpressure to PubSub when it fills up
   - The processed message queue controls the rate of Elasticsearch operations
   - This prevents any single component from overwhelming the others

### How the Dual Queue System Works

1. **Raw Message Queue (RawMessageAccumulator)**:
   - Receives messages directly from PubSub
   - Batches messages based on size and time thresholds
   - When full (>80%), pauses PubSub polling to prevent memory exhaustion
   - Provides messages to the ElasticsearchSynchronizer for processing

2. **Processed Message Queue (MessageAccumulator)**:
   - Receives fully processed messages with data from PostgreSQL
   - Batches messages for efficient Elasticsearch operations
   - Uses adaptive batch sizing based on message size and system load
   - Feeds the WorkerManager which handles concurrent Elasticsearch operations

3. **Flow Between Queues**:
   - Messages flow from PubSub → Raw Queue → Processing → Processed Queue → Elasticsearch
   - Each stage can operate at its own pace, with queues absorbing temporary rate differences
   - If any stage slows down, the queues provide buffering to prevent message loss

4. **Monitoring and Control**:
   - Both queues report their size and fullness percentage
   - The system adjusts processing rates based on queue states
   - Metrics API exposes queue statistics for monitoring

This dual queue architecture provides several benefits:

- **Resilience**: The system can continue receiving messages even when processing or Elasticsearch is temporarily unavailable
- **Performance**: Each stage can operate at its optimal batch size and rate
- **Resource Efficiency**: The system can adapt to varying load conditions without wasting resources
- **Visibility**: Queue metrics provide insight into system performance and bottlenecks

## Adding New Subscription Types

To add a new subscription type:

1. Define the subscription configuration in `subscription_config.py`:
   ```python
   "new-subscription-name": {
       "es_index": "target_index",
       "operation_type": "update",
       "query": "query_name",
       "id_fields": ["field1", "field2"],
       "doc_id_template": "{field1}|{field2}"
   }
   ```

2. Add a new query template in `query_utils.py` if needed:
   ```python
   query_by_config = {
       "query_name": """
           WITH field_ids AS (
               SELECT
                   unnest(%(field1s)s::uuid[]) AS field1,
                   unnest(%(field2s)s::uuid[]) AS field2
           ),
           -- Rest of your query
       """
   }
   ```

3. Update the subscription list in your configuration file or environment variables.

## Troubleshooting

### Common Issues

- **Connection Timeout**: Check Elasticsearch connectivity and credentials
- **High Backpressure**: Increase worker threads or reduce batch size
- **Memory Pressure**: Adjust queue size and max PubSub message settings
- **Missing Secrets**: Verify Secret Manager access and secret names
- **JSON Serialization Errors**: The system automatically converts Decimal values to float for JSON serialization. If you encounter other serialization issues, check for custom data types in your messages
- **PostgreSQL Connection Issues**: Check database credentials and connectivity. The system will retry connections with exponential backoff
- **Elasticsearch Mapping Errors**: The system transforms application_answers to match Elasticsearch's schema. If you encounter mapping errors, check that the Elasticsearch index template matches the expected format
- **Field Limit Exceeded**: If you encounter "Limit of total fields exceeded" errors, the system will automatically limit the number of fields to prevent this error

### Logs

Look for log entries bounded by `=====` for performance metrics, which include:

- PubSub message rates
- Record processing rates
- Queue fullness
- Error rates
- Processing timings

## Scripts and Documentation

### Schema Management
- **[Schema Reindexing Guide](scripts/schema-reindex.md)** - Comprehensive documentation for the automated schema reindexing script that safely updates Elasticsearch indices when templates change
- **[Manual Backfill Guide](scripts/manual-backfill.md)** - Step-by-step instructions for manually backfilling data into Elasticsearch indices

### Available Scripts
- `scripts/schema_reindex.py` - Automated schema-aware reindexing with zero-downtime deployment
- `scripts/backfill_cluster_data.py` - Data backfill script for populating indices

## Recent Improvements

### Elasticsearch Schema Compatibility

- **Nested Field Handling**: Added support for Elasticsearch's nested field mapping for application_answers
- **Field Transformation**: Automatically transforms application_answers to match Elasticsearch's expected format
- **Flattening Complex Objects**: Implemented dictionary flattening to handle deeply nested objects
- **Field Limiting**: Added intelligent field limiting to prevent exceeding Elasticsearch's field limit

### Performance Monitoring Enhancements

- **Metrics API Improvements**: Enhanced metrics API to provide more detailed performance data
- **Historical Rate Tracking**: Added tracking of historical processing rates for better trend analysis
- **JSON Metrics Endpoint**: Improved the /metrics endpoint to provide comprehensive performance data
- **Real-time Rate Display**: Added real-time display of message processing rates in logs

### Data Generation Improvements

- **Dynamic Query Limits**: Modified data generation scripts to use dynamic query limits based on message count
- **Randomized Data Selection**: Added randomization to data selection for more diverse test data
- **Improved Query Parameters**: Enhanced SQL queries with proper parameterization for better security
- **Scaling Algorithm**: Implemented a scaling algorithm for fetching appropriate amounts of test data

### PostgreSQL Connection Optimization

- **Connection Pooling**: The system now uses a shared PostgreSQL connection instance instead of creating a new connection for each message
- **Retry Logic**: Added exponential backoff retry for PostgreSQL connections
- **Error Handling**: Improved error handling for database operations
- **Query Simplification**: Simplified database queries for better performance
- **Optimized Query**: Added query_utils.py with an optimized SQL query for batch operations
- **Batch Processing**: Implemented batch query capabilities for processing multiple records efficiently
- **Connection Management**: Added pg_connection_pool.py for better connection management

### JSON Serialization Enhancements

- **Decimal Handling**: Added automatic conversion of Decimal values to float for JSON serialization
- **Custom JSON Encoder**: Implemented a custom JSON encoder to handle special data types
- **MessagePack Integration**: Updated MessagePack serializer to handle Decimal values

### Error Handling Improvements

- **Metrics API Resilience**: Added error handling to the metrics API to prevent crashes
- **Graceful Degradation**: The system now falls back to default values when metrics collection fails
- **Detailed Error Logging**: Enhanced error logging for better troubleshooting

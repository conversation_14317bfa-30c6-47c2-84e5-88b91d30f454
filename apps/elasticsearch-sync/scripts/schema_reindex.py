#!/usr/bin/env python3
"""
Schema-aware reindexing script for Elasticsearch.

This script handles reindexing when there are changes to index schema templates.
It compares the current template with the new template and performs a safe reindex
operation using Elasticsearch's reindex API.
"""

import argparse
import json
import logging
import os
import re
import sys
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from rich import print
from rich.logging import RichHandler
from rich.panel import Panel
from rich.progress import BarColumn, Progress, TextColumn, TimeElapsedColumn, TimeRemainingColumn

load_dotenv()

# Logger will be configured in main() after parsing CLI arguments
logger = None


def configure_logging(log_level: str = "INFO") -> logging.Logger:
    """
    Configure logging with the specified level.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)

    Returns:
        Configured logger instance
    """
    # Clear any existing handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Configure rich logging with the specified level
    FORMAT = "%(message)s"
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=FORMAT,
        datefmt="[%X]",
        handlers=[RichHandler()],
        force=True,  # Force reconfiguration
    )

    # Get and configure the logger
    configured_logger = logging.getLogger(__name__)
    configured_logger.setLevel(getattr(logging, log_level.upper()))

    return configured_logger


class SchemaReindexer:
    """
    Handles schema-aware reindexing operations for Elasticsearch indices.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the schema reindexer.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.es_client = None

    def wait_for_elasticsearch(self, max_retries=30, retry_interval=10):
        """Wait for Elasticsearch to become available."""
        for attempt in range(max_retries):
            try:
                info = self.es_client.info()
                logger.debug(f"Elasticsearch Cluster info: {info}")
                if self.es_client.ping():
                    logger.info("Elasticsearch is available (client ping)")
                    return True
                else:
                    logger.warning(
                        f"Elasticsearch ping failed (attempt {attempt + 1}/{max_retries})"
                    )
            except Exception as e:
                logger.warning(
                    f"Elasticsearch not available yet: {str(e)[:100]}... Retrying in {retry_interval} seconds... (attempt {attempt + 1}/{max_retries})"
                )

            time.sleep(retry_interval)

        logger.error(f"Elasticsearch not available after {max_retries} retries")
        return False

    def connect(self) -> bool:
        """
        Connect to Elasticsearch cluster.

        Returns:
            True if connection successful, False otherwise
        """
        # Connect to Elasticsearch
        if self.config["use_local_es"] or "localhost" in self.config["es_endpoint"]:
            self.es_client = Elasticsearch(
                hosts=[self.config["es_endpoint"]],
                request_timeout=30,
                retry_on_timeout=True,
                max_retries=3,
                verify_certs=False,  # For local development only
            )
        else:
            self.es_client = Elasticsearch(
                hosts=[self.config["es_endpoint"]],
                basic_auth=(self.config["es_username"], self.config["es_password"]),
                request_timeout=30,
                max_retries=5,
                retry_on_timeout=True,
                http_compress=True,  # Enable HTTP compression
                retry_on_status=[429, 502, 503, 504],  # Retry on common errors
                connections_per_node=16,  # More concurrent connections
                # We have self-signed certs in the trust chain for https://es.*.bybeam.co
                # `openssl s_client -showcerts -connect es.dev.bybeam.co:443`
                verify_certs=False,  # Disable SSL certificate verification
            )

        if not self.es_client or not self.wait_for_elasticsearch():
            logger.error("Failed to connect to Elasticsearch")
            return False

        logger.info("Successfully connected to Elasticsearch")
        return True

    def load_schema_template(self, template_path: str) -> Optional[Dict[str, Any]]:
        """
        Load schema template from JSON file.

        Args:
            template_path: Path to the schema template JSON file

        Returns:
            Schema template dictionary or None if loading fails
        """
        try:
            with open(template_path, "r") as f:
                template = json.load(f)
            logger.info(f"Loaded schema template from {template_path}")
            return template
        except Exception as e:
            logger.error(f"Failed to load schema template from {template_path}: {e}")
            return None

    def get_current_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        Get the current index template from Elasticsearch.

        Args:
            template_name: Name of the index template

        Returns:
            Current template dictionary or None if not found
        """
        try:
            response = self.es_client.indices.get_index_template(name=template_name)
            if response and "index_templates" in response and response["index_templates"]:
                return response["index_templates"][0]["index_template"]
            else:
                logger.warning(f"Index template '{template_name}' not found")
                return None
        except Exception as e:
            logger.error(f"Failed to get current template '{template_name}': {e}")
            return None

    def _get_nested_diff(
        self, current: Dict[str, Any], new: Dict[str, Any], path: str = ""
    ) -> List[str]:
        """
        Get human-readable differences between nested dictionaries.

        Args:
            current: Current dictionary
            new: New dictionary
            path: Current path for nested keys

        Returns:
            List of difference descriptions
        """
        diffs = []
        all_keys = set(current.keys()) | set(new.keys())

        for key in sorted(all_keys):
            current_path = f"{path}.{key}" if path else key

            if key not in current:
                diffs.append(f"+ {current_path}: {new[key]}")
            elif key not in new:
                diffs.append(f"- {current_path}: {current[key]}")
            elif current[key] != new[key]:
                if isinstance(current[key], dict) and isinstance(new[key], dict):
                    # Recursively diff nested objects
                    nested_diffs = self._get_nested_diff(current[key], new[key], current_path)
                    diffs.extend(nested_diffs)
                else:
                    diffs.append(f"~ {current_path}: {current[key]} → {new[key]}")

        return diffs

    def compare_templates(
        self, current: Dict[str, Any], new: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        Compare two index templates to identify differences.

        Args:
            current: Current template dictionary
            new: New template dictionary

        Returns:
            Tuple of (templates_differ, list_of_differences)
        """
        differences = []

        # Compare mappings
        current_mappings = current.get("template", {}).get("mappings", {})
        new_mappings = new.get("template", {}).get("mappings", {})

        if current_mappings != new_mappings:
            differences.append("mappings")
            mapping_diffs = self._get_nested_diff(current_mappings, new_mappings)
            logger.info("Mapping differences:")
            for diff in mapping_diffs:
                logger.info(f"  {diff}")

        # Compare settings (excluding dynamic settings like replicas)
        current_settings = current.get("template", {}).get("settings", {})
        new_settings = new.get("template", {}).get("settings", {})

        # Filter out dynamic settings that don't require reindexing
        dynamic_settings = {
            "number_of_replicas",
            "refresh_interval",
            "creation_date",
            "provided_name",
            "uuid",
            "version",
        }

        def normalize_settings(settings):
            """Normalize settings by removing dynamic fields and handling nested structures."""
            if not settings:
                return {}

            # Handle both flat and nested index settings
            index_settings = settings.get("index", settings)

            def normalize_value(value):
                """Normalize values to handle string/integer type mismatches."""
                if isinstance(value, str) and value.isdigit():
                    return int(value)
                elif isinstance(value, dict):
                    return {k: normalize_value(v) for k, v in value.items()}
                return value

            return {
                k: normalize_value(v)
                for k, v in index_settings.items()
                if k not in dynamic_settings and not k.startswith("_")
            }

        current_static = normalize_settings(current_settings)
        new_static = normalize_settings(new_settings)

        if current_static != new_static:
            differences.append("settings")
            settings_diffs = self._get_nested_diff(current_static, new_static)
            logger.info("Settings differences:")
            for diff in settings_diffs:
                logger.info(f"  {diff}")

        # Compare index patterns
        current_patterns = set(current.get("index_patterns", []))
        new_patterns = set(new.get("index_patterns", []))

        if current_patterns != new_patterns:
            differences.append("index_patterns")
            removed = current_patterns - new_patterns
            added = new_patterns - current_patterns
            if removed:
                logger.info(f"  - index_patterns: {list(removed)}")
            if added:
                logger.info(f"  + index_patterns: {list(added)}")

        # Compare version if present
        current_version = current.get("version")
        new_version = new.get("version")

        if current_version != new_version and (
            current_version is not None or new_version is not None
        ):
            differences.append("version")
            logger.info(f"  ~ version: {current_version} → {new_version}")

        logger.debug(
            f"Template comparison result: {len(differences)} differences found: {differences}"
        )
        return len(differences) > 0, differences

    def get_matching_indices(self, index_patterns: List[str]) -> List[str]:
        """
        Get all indices that match the given patterns.

        Args:
            index_patterns: List of index patterns

        Returns:
            List of matching index names
        """
        try:
            all_indices = self.es_client.indices.get_alias(index="*")
            matching_indices = []

            for index_name in all_indices.keys():
                # Skip timestamped backup indices to avoid unnecessary work
                if re.match(r".*_(backup|temp)_\d{8}_\d{6}$", index_name):
                    continue

                for pattern in index_patterns:
                    # Simple pattern matching (convert * to regex)
                    regex_pattern = pattern.replace("*", ".*")
                    if re.match(f"^{regex_pattern}$", index_name):
                        matching_indices.append(index_name)
                        break

            logger.info(f"Found {len(matching_indices)} indices matching patterns {index_patterns}")
            return matching_indices

        except Exception as e:
            logger.error(f"Failed to get matching indices: {e}")
            return []

    def update_template(self, template_name: str, new_template: Dict[str, Any]) -> bool:
        """
        Update the index template in Elasticsearch.

        Args:
            template_name: Name of the template to update
            new_template: New template configuration

        Returns:
            True if update successful, False otherwise
        """
        try:
            self.es_client.indices.put_index_template(name=template_name, **new_template)
            logger.info(f"Successfully updated index template '{template_name}'")
            return True
        except Exception as e:
            logger.error(f"Failed to update index template '{template_name}': {e}")
            return False

    def reindex_with_new_mapping(self, source_index: str, target_index: str) -> bool:
        """
        Reindex data from source to target index with new mapping.

        Args:
            source_index: Source index name
            target_index: Target index name

        Returns:
            True if reindex successful, False otherwise
        """
        try:
            # Prepare reindex request
            reindex_body = {"source": {"index": source_index}, "dest": {"index": target_index}}

            logger.info(f"Starting reindex from '{source_index}' to '{target_index}'")

            # Start reindex operation
            response = self.es_client.reindex(**reindex_body, wait_for_completion=False)
            task_id = response.get("task")

            if not task_id:
                logger.error("Failed to start reindex task")
                return False

            logger.info(f"Reindex task started with ID: {task_id}")

            # Monitor the reindex task with rich progress bar
            with Progress(
                TextColumn("[bold blue]Reindexing", justify="right"),
                BarColumn(bar_width=None),
                "[progress.percentage]{task.percentage:>3.1f}%",
                "•",
                TextColumn("[bold green]{task.completed}/{task.total}"),
                "•",
                TimeElapsedColumn(),
                "•",
                TimeRemainingColumn(),
            ) as progress:
                task = None
                # ---------------------------------------------------------
                # Timeout handling
                # ---------------------------------------------------------
                # Capture the start time once before entering the polling loop
                start_time = time.time()
                # Allow override via self.config; default to one hour (3600 s)
                timeout = self.config.get("reindex_timeout", 3600)

                while True:
                    # Abort the operation if it exceeds the configured timeout
                    if time.time() - start_time > timeout:
                        logger.error(f"Reindex timed out after {timeout} seconds")
                        return False

                    try:
                        task_status = self.es_client.tasks.get(task_id=task_id)
                        completed = task_status.get("completed", False)

                        if completed:
                            task_response = task_status.get("response", {})
                            total = task_response.get("total", 0)
                            created = task_response.get("created", 0)
                            updated = task_response.get("updated", 0)

                            if task:
                                progress.update(task, completed=total)

                            logger.info(
                                f"Reindex completed: {total} total, {created} created, {updated} updated"
                            )
                            return True
                        else:
                            # Show progress
                            status = task_status.get("task", {}).get("status", {})
                            total = status.get("total", 0)
                            created = status.get("created", 0)

                            if total > 0:
                                if task is None:
                                    task = progress.add_task("reindex", total=total)
                                progress.update(task, completed=created)

                            time.sleep(5)  # Wait 5 seconds before checking again

                    except Exception as e:
                        logger.error(f"Error monitoring reindex task: {e}")
                        time.sleep(10)  # Wait longer on error

        except Exception as e:
            logger.error(f"Failed to reindex from '{source_index}' to '{target_index}': {e}")
            return False

    def create_temporary_index(self, base_name: str) -> str:
        """
        Create a temporary index with the new template applied.

        Args:
            base_name: Base name for the temporary index

        Returns:
            Name of the created temporary index
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_index_name = f"{base_name}_temp_{timestamp}"

        try:
            # Create the temporary index (template will be automatically applied)
            self.es_client.indices.create(index=temp_index_name)
            logger.info(f"Created temporary index: {temp_index_name}")
            return temp_index_name
        except Exception as e:
            logger.error(f"Failed to create temporary index: {e}")
            raise

    def atomic_index_swap(self, old_index: str, new_index: str) -> bool:
        """
        Perform atomic index swap using aliases.

        Args:
            old_index: Current index name
            new_index: New index name

        Returns:
            True if swap successful, False otherwise
        """
        try:
            # Get existing aliases for the old index
            aliases_response = self.es_client.indices.get_alias(index=old_index)
            existing_aliases = list(aliases_response.get(old_index, {}).get("aliases", {}).keys())

            # Create backup alias for old index
            backup_alias = f"{old_index}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Prepare alias operations
            alias_actions = []

            # Remove old aliases and add backup alias for old index
            for alias in existing_aliases:
                alias_actions.append({"remove": {"index": old_index, "alias": alias}})
                alias_actions.append({"add": {"index": new_index, "alias": alias}})

            # Add backup alias to old index
            alias_actions.append({"add": {"index": old_index, "alias": backup_alias}})

            # Execute all alias operations atomically
            self.es_client.indices.update_aliases(actions=alias_actions)

            logger.info(f"Successfully swapped indices: {old_index} -> {new_index}")
            logger.info(f"Old index available as: {backup_alias}")
            return True

        except Exception as e:
            logger.error(f"Failed to perform atomic index swap: {e}")
            return False

    def cleanup_old_index(self, old_index: str, keep_backup: bool = True) -> bool:
        """
        Clean up old index after successful reindexing.

        Args:
            old_index: Name of the old index to clean up
            keep_backup: Whether to keep a backup alias

        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if not keep_backup:
                # Remove the old index entirely
                self.es_client.indices.delete(index=old_index)
                logger.info(f"Deleted old index: {old_index}")
            else:
                logger.info(f"Keeping old index as backup: {old_index}")

            return True
        except Exception as e:
            logger.error(f"Failed to cleanup old index: {e}")
            return False

    def reindex_for_schema_changes(
        self,
        template_name: str,
        template_path: str,
        dry_run: bool = False,
        force: bool = False,
        keep_backup: bool = True,
    ) -> bool:
        """
        Main method to handle reindexing when schema changes are detected.

        Args:
            template_name: Name of the index template
            template_path: Path to the new schema template file
            dry_run: If True, only show what would be done
            force: If True, reindex even if no changes detected
            keep_backup: Whether to keep backup of old indices

        Returns:
            True if reindexing successful, False otherwise
        """
        logger.info(f"Starting schema reindex process for template: {template_name}")

        # Load new template
        new_template = self.load_schema_template(template_path)
        if not new_template:
            return False

        # Get current template
        current_template = self.get_current_template(template_name)

        if current_template and not force:
            # Compare templates
            templates_differ, differences = self.compare_templates(current_template, new_template)

            if not templates_differ:
                logger.info("No schema changes detected. Skipping reindex.")
                return True
            else:
                logger.info(f"Schema changes detected: {', '.join(differences)}")
        else:
            logger.info("No current template found or force flag set. Proceeding with reindex.")

        if dry_run:
            logger.info("DRY RUN: Would update template and reindex matching indices")
            index_patterns = new_template.get("index_patterns", [])
            matching_indices = self.get_matching_indices(index_patterns)
            for index in matching_indices:
                logger.info(f"DRY RUN: Would reindex {index}")
            return True

        # Update the template
        if not self.update_template(template_name, new_template):
            return False

        # Get indices that match the template patterns
        index_patterns = new_template.get("index_patterns", [])
        matching_indices = self.get_matching_indices(index_patterns)

        if not matching_indices:
            logger.info("No matching indices found. Template updated successfully.")
            return True

        # Reindex each matching index
        success_count = 0
        for index_name in matching_indices:
            try:
                logger.info(f"Processing index: {index_name}")

                # Create temporary index with new template
                temp_index = self.create_temporary_index(index_name)

                # Reindex data to temporary index
                if self.reindex_with_new_mapping(index_name, temp_index):
                    # Perform atomic swap
                    if self.atomic_index_swap(index_name, temp_index):
                        success_count += 1
                        logger.info(f"Successfully reindexed: {index_name}")

                        # Cleanup if requested
                        if not keep_backup:
                            self.cleanup_old_index(index_name, keep_backup=False)
                    else:
                        logger.error(f"Failed to swap indices for: {index_name}")
                        # Clean up temporary index on failure
                        try:
                            self.es_client.indices.delete(index=temp_index)
                        except Exception:
                            pass
                else:
                    logger.error(f"Failed to reindex: {index_name}")
                    # Clean up temporary index on failure
                    try:
                        self.es_client.indices.delete(index=temp_index)
                    except Exception:
                        pass

            except Exception as e:
                logger.error(f"Error processing index {index_name}: {e}")

        total_indices = len(matching_indices)
        logger.info(f"Reindex completed: {success_count}/{total_indices} indices successful")

        return success_count == total_indices


def main():
    """Main entry point for the script."""
    global logger

    parser = argparse.ArgumentParser(
        description="Reindex Elasticsearch indices when schema template changes are detected"
    )
    parser.add_argument("template_name", help="Name of the index template to update")
    parser.add_argument("template_path", help="Path to the new schema template JSON file")
    parser.add_argument(
        "--dry-run", action="store_true", help="Show what would be done without actually doing it"
    )
    parser.add_argument(
        "--force", action="store_true", help="Force reindex even if no schema changes are detected"
    )
    parser.add_argument("--no-backup", action="store_true", help="Don't keep backup of old indices")
    parser.add_argument(
        "--use-local-es", action="store_true", help="Use local Elasticsearch instance"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set the logging level",
    )
    parser.add_argument(
        "--give-me-a-sign", action="store_true", help="Let the master help you find your way"
    )

    args = parser.parse_args()

    # Configure logging with the specified log level
    logger = configure_logging(args.log_level)

    # Prepare configuration
    config = {
        "use_local_es": args.use_local_es,
        "es_endpoint": os.getenv("ES_ENDPOINT"),
        "es_username": os.getenv("ES_USERNAME"),
        "es_password": os.getenv("ES_PASSWORD"),
    }

    if args.give_me_a_sign:
        extract_oblique_strategy()
        sys.exit(0)

    # Initialize reindexer
    reindexer = SchemaReindexer(config)

    # Connect to Elasticsearch
    if not reindexer.connect():
        logger.error("Failed to connect to Elasticsearch")
        sys.exit(1)

    # Perform reindexing
    success = reindexer.reindex_for_schema_changes(
        template_name=args.template_name,
        template_path=args.template_path,
        dry_run=args.dry_run,
        force=args.force,
        keep_backup=not args.no_backup,
    )

    if success:
        logger.info("Schema reindex process completed successfully")
        sys.exit(0)
    else:
        logger.error("Schema reindex process failed")
        sys.exit(1)


def extract_oblique_strategy():
    """
    Extract all H3 elements from a given URL

    Args:
        url (str): The URL to scrape

    Returns:
        list: List of H3 text content
    """
    import requests
    from bs4 import BeautifulSoup

    # print("You have requested a sign. Incoming...\n\n")

    try:
        # Send GET request to the URL
        response = requests.get("https://stoney.sb.org/eno/oblique.html", timeout=10)
        response.raise_for_status()  # Raise an exception for bad status codes

        # Parse the HTML content
        soup = BeautifulSoup(response.content, "html.parser")

        # Find all H3 elements
        h3_elements = soup.find_all("h3")

        # Extract text from each H3 element
        h3_texts = []
        for h3 in h3_elements:
            text = h3.get_text(strip=True)
            if text:  # Only add non-empty text
                h3_texts.append(text)

        if not h3_texts or len(h3_texts) == 0:
            raise Exception("No strategies found")

        print(
            Panel.fit(
                "Brian says:"
                + "\n\n"
                + "[bold yellow]"
                + "\n".join(h3_texts)
                + "[/bold yellow]"
                + "\n\n"
                + "Good luck, brother.",
                border_style="green",
            )
        )
    except Exception as e:
        logger.error(f"Error fetching oblique strategy: {e}")
        print(
            Panel.fit(
                "Your attempt to gain wisdom has failed. Better luck next time.",
                border_style="red",
            )
        )


if __name__ == "__main__":
    main()

import { useMutation } from '@apollo/client';
import { useCallback } from 'react';
import GeneratePresetGuestTokenMutation from './GeneratePresetGuestTokenMutation.graphql';

interface PresetGuestTokenInput {
  partnerId: string;
  dashboardId: string;
  workspaceId?: string;
}

interface PresetGuestTokenResponse {
  token: string;
  expiresAt: string;
}

export const usePresetGuestToken = () => {
  const [generateGuestToken, { loading: generateLoading, error: generateError }] = useMutation<
    { preset: { generatePresetGuestToken: PresetGuestTokenResponse } },
    { input: PresetGuestTokenInput }
  >(GeneratePresetGuestTokenMutation);

  const getGuestToken = useCallback(
    async (input: PresetGuestTokenInput): Promise<PresetGuestTokenResponse> => {
      try {
        const result = await generateGuestToken({
          variables: { input },
        });

        if (!result.data?.preset?.generatePresetGuestToken?.token) {
          throw new Error('No token received from server');
        }

        return result.data.preset.generatePresetGuestToken;
      } catch (err) {
        console.error('Failed to generate Preset guest token:', err);
        throw new Error(
          `Failed to generate Preset guest token: ${err instanceof Error ? err.message : 'Unknown error'}`,
        );
      }
    },
    [generateGuestToken],
  );

  return {
    getGuestToken,
    loading: generateLoading,
    error: generateError,
  };
};

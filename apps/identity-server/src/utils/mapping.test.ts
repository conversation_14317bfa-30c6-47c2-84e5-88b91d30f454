import { User as IdentityUser } from '@bybeam/identity-client/types';
import { User as CoreUser } from '@bybeam/platform-types';
import { DecodedIdToken, Tenant as FirebaseTenant } from 'firebase-admin/auth';
import { describe, expect, it, vi } from 'vitest';
import {
  mapCoreUserToIdentityService,
  mapGCIPToIdentityUser,
  mapIdentityToCoreUser,
  mapRelationshipFilter,
  mapTenant,
  mapUserUpdateToGCIP,
  removeEmpty,
} from './mapping.js';

vi.mock('node:crypto', () => ({
  randomUUID: () => 'mockRandomUUID',
}));

describe('mapCoreUserToIdentityService', () => {
  it('should set "user" to null and use the existing user id one is found via email', () => {
    expect(
      mapCoreUserToIdentityService({
        user: {
          id: 'mockCoreUserId',
          email: '<EMAIL>',
          name: 'Mock Name',
        } as unknown as CoreUser,
        partnerId: 'mockPartnerId',
        tenantId: 'app-mock-123',
        existingIdentityUsers: [
          { id: 'mockIdentityUserId', email: '<EMAIL>' },
        ] as IdentityUser[],
        duplicatePhones: [],
      }),
    ).toEqual({
      user: null,
      gcipUser: { tenantId: 'app-mock-123', uid: 'mockRandomUUID', userId: 'mockIdentityUserId' },
      applicant: { id: 'mockCoreUserId', partnerId: 'mockPartnerId', userId: 'mockIdentityUserId' },
      gcipInsert: {
        uid: 'mockRandomUUID',
        email: '<EMAIL>',
        displayName: 'Mock Name',
        emailVerified: false,
      },
    });
  });
  it('should include advocate data if the core user is an admin', () => {
    expect(
      mapCoreUserToIdentityService({
        user: {
          id: 'mockCoreUserId',
          email: '<EMAIL>',
          name: 'Mock Name',
          admin: { id: 'mockCoreAdminId' },
        } as unknown as CoreUser,
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        existingIdentityUsers: [
          { id: 'mockIdentityUserId', email: '<EMAIL>' },
        ] as IdentityUser[],
        duplicatePhones: [],
      }),
    ).toEqual({
      user: null,
      gcipUser: { tenantId: 'adv-mock-123', uid: 'mockRandomUUID', userId: 'mockIdentityUserId' },
      advocate: {
        id: 'mockCoreAdminId',
        coreUserId: 'mockCoreUserId',
        partnerId: 'mockPartnerId',
        userId: 'mockIdentityUserId',
      },
      gcipInsert: {
        uid: 'mockRandomUUID',
        email: '<EMAIL>',
        displayName: 'Mock Name',
        emailVerified: false,
      },
    });
  });
  it('should include a buffer of the password hash in the gcip insert', () => {
    expect(
      mapCoreUserToIdentityService({
        user: {
          id: 'mockCoreUserId',
          email: '<EMAIL>',
          name: 'Mock Name',
          password: 'ISwearIAmHashed',
          admin: { id: 'mockCoreAdminId' },
        } as unknown as CoreUser,
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        existingIdentityUsers: [
          { id: 'mockIdentityUserId', email: '<EMAIL>' },
        ] as IdentityUser[],
        duplicatePhones: [],
      }),
    ).toEqual({
      user: null,
      gcipUser: { tenantId: 'adv-mock-123', uid: 'mockRandomUUID', userId: 'mockIdentityUserId' },
      advocate: {
        id: 'mockCoreAdminId',
        coreUserId: 'mockCoreUserId',
        partnerId: 'mockPartnerId',
        userId: 'mockIdentityUserId',
      },
      gcipInsert: {
        uid: 'mockRandomUUID',
        email: '<EMAIL>',
        displayName: 'Mock Name',
        emailVerified: false,
      },
    });
  });
  it('should include the phone if it is not in the dupe list', () => {
    expect(
      mapCoreUserToIdentityService({
        user: {
          id: 'mockCoreUserId',
          email: '<EMAIL>',
          name: 'Mock Name',
          phone: '+12123435445',
          admin: { id: 'mockCoreAdminId' },
        } as unknown as CoreUser,
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        existingIdentityUsers: [] as IdentityUser[],
        duplicatePhones: ['+12123435446'],
      }),
    ).toEqual({
      user: {
        id: 'mockRandomUUID',
        email: '<EMAIL>',
        name: 'Mock Name',
        phone: '+12123435445',
        verifiedEmail: false,
        createdAt: undefined,
      },
      gcipUser: { tenantId: 'adv-mock-123', uid: 'mockRandomUUID', userId: 'mockRandomUUID' },
      advocate: {
        id: 'mockCoreAdminId',
        coreUserId: 'mockCoreUserId',
        partnerId: 'mockPartnerId',
        userId: 'mockRandomUUID',
      },
      gcipInsert: {
        uid: 'mockRandomUUID',
        email: '<EMAIL>',
        displayName: 'Mock Name',
        emailVerified: false,
        phoneNumber: '+12123435445',
      },
    });
  });
  it('should drop the phone if it is in the dupe list', () => {
    expect(
      mapCoreUserToIdentityService({
        user: {
          id: 'mockCoreUserId',
          email: '<EMAIL>',
          name: 'Mock Name',
          phone: '+12123435445',
          admin: { id: 'mockCoreAdminId' },
        } as unknown as CoreUser,
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        existingIdentityUsers: [] as IdentityUser[],
        duplicatePhones: ['+12123435445'],
      }),
    ).toEqual({
      user: {
        id: 'mockRandomUUID',
        email: '<EMAIL>',
        name: 'Mock Name',
        verifiedEmail: false,
        createdAt: undefined,
      },
      gcipUser: { tenantId: 'adv-mock-123', uid: 'mockRandomUUID', userId: 'mockRandomUUID' },
      advocate: {
        id: 'mockCoreAdminId',
        coreUserId: 'mockCoreUserId',
        partnerId: 'mockPartnerId',
        userId: 'mockRandomUUID',
      },
      gcipInsert: {
        uid: 'mockRandomUUID',
        email: '<EMAIL>',
        displayName: 'Mock Name',
        emailVerified: false,
      },
    });
  });
});

describe('mapGCIPToIdentityUser', () => {
  it('should map the GCIP fields to the identity user fields', () => {
    expect(
      mapGCIPToIdentityUser({ uid: 'mockGCIPUID', email: '<EMAIL>' } as DecodedIdToken),
    ).toEqual({
      email: '<EMAIL>',
      name: '<EMAIL>',
    });
    expect(
      mapGCIPToIdentityUser({
        email: '<EMAIL>',
        displayName: 'Mock Name',
        phone_number: '+13334445255',
        email_verified: true,
      } as unknown as DecodedIdToken),
    ).toEqual({
      email: '<EMAIL>',
      name: 'Mock Name',
      phone: '+13334445255',
      verifiedEmail: true,
    });
  });
});

describe('mapIdentityToCoreUser', () => {
  it('should map identity user fields to corresponding core user fields', () => {
    expect(
      mapIdentityToCoreUser({ id: 'mockId', email: '<EMAIL>' }, 'mockPartnerId'),
    ).toEqual({
      id: 'mockId',
      email: '<EMAIL>',
      partnerId: 'mockPartnerId',
    });
    expect(
      mapIdentityToCoreUser(
        {
          id: 'mockId',
          email: '<EMAIL>',
          name: 'Mock Name',
          phone: '+12223334455',
          verifiedEmail: true,
        },
        'mockPartnerId',
      ),
    ).toEqual({
      id: 'mockId',
      email: '<EMAIL>',
      partnerId: 'mockPartnerId',
      name: 'Mock Name',
      phone: '+12223334455',
      validatedEmail: true,
    });
  });
});

describe('mapTenant', () => {
  it('should return a tenant obj from firebase tenant class', () => {
    expect(
      mapTenant({
        toJSON: vi.fn().mockReturnValue({
          tenantId: 'local-app-panopticon-gh7uk',
          displayName: 'local-app-panopticon',
          emailSignInConfig: { enabled: true, passwordRequired: true },
          anonymousSignInEnabled: true,
          recaptchaConfig: {
            emailPasswordEnforcementState: 'AUDIT',
            managedRules: undefined,
            recaptchaKeys: undefined,
            useAccountDefender: true,
          },
          passwordPolicyConfig: {
            enforcementState: 'ENFORCE',
            constraints: {
              requireLowercase: true,
              requireUppercase: true,
              requireNonAlphanumeric: true,
              requireNumeric: true,
              minLength: 6,
              maxLength: 12,
            },
            forceUpgradeOnSignin: true,
          },
          emailPrivacyConfig: { enableImprovedEmailPrivacy: true },
        }),
      } as unknown as FirebaseTenant),
    ).toEqual({
      tenantId: 'local-app-panopticon-gh7uk',
      displayName: 'local-app-panopticon',
      options: {
        emailSignInConfig: { enabled: true, passwordRequired: true },
        anonymousSignInEnabled: true,
        recaptchaConfig: {
          emailPasswordEnforcementState: 'AUDIT',
          managedRules: undefined,
          recaptchaKeys: undefined,
          useAccountDefender: true,
        },
        passwordPolicyConfig: {
          enforcementState: 'ENFORCE',
          constraints: {
            requireLowercase: true,
            requireUppercase: true,
            requireNonAlphanumeric: true,
            requireNumeric: true,
            minLength: 6,
            maxLength: 12,
          },
          forceUpgradeOnSignin: true,
        },
        emailPrivacyConfig: { enableImprovedEmailPrivacy: true },
      },
    });
  });
  it('should throw an error if firebase tenant is not structured correctly', () => {
    expect(() => mapTenant({} as unknown as FirebaseTenant)).toThrowError(
      new Error('can not map firebase tenant'),
    );
  });
});

describe('mapUserUpdateToGCIP', () => {
  it('should map the core user update to GCIP fields', () => {
    expect(
      mapUserUpdateToGCIP({
        name: 'Mock User',
        email: '<EMAIL>',
        phone: '+***********',
      }),
    ).toEqual({
      displayName: 'Mock User',
      email: '<EMAIL>',
      phoneNumber: '+***********',
    });
    expect(
      mapUserUpdateToGCIP({
        email: '<EMAIL>',
      }),
    ).toEqual({
      email: '<EMAIL>',
    });
  });
});

describe('removeEmpty', () => {
  it('should remove nullish values from object', () => {
    expect(
      removeEmpty({
        anonymousSignInEnabled: false,
        displayName: '',
        emailPrivacyConfig: null,
        emailSignInConfig: {
          enabled: true,
          passwordRequired: true,
        },
        multiFactorConfig: {
          factorIds: [],
          providerConfigs: [],
          state: 'ENABLED',
        },
        passwordPolicyConfig: null,
        recaptchaConfig: null,
      }),
    ).toEqual({
      anonymousSignInEnabled: false,
      emailSignInConfig: {
        enabled: true,
        passwordRequired: true,
      },
      multiFactorConfig: {
        factorIds: [],
        providerConfigs: [],
        state: 'ENABLED',
      },
    });
  });
  it('should return the argument untouched if undefined', () => {
    expect(removeEmpty(undefined as unknown as Record<string, unknown>)).toBeUndefined();
  });
});

describe('mapRelationshipFilter', () => {
  it('should correctly map all fields when provided', () => {
    const input = {
      optionalRelation: 'manager',
      resourceType: 'organization',
      optionalResourceId: 'mockPartnerId',
      optionalSubjectFilter: {
        subjectType: 'user',
        optionalSubjectId: 'mockUserId',
      },
    };

    const result = mapRelationshipFilter(input);

    expect(result).toEqual({
      optionalRelation: 'manager',
      resourceType: 'organization',
      optionalResourceId: 'mockPartnerId',
      optionalResourceIdPrefix: '',
      optionalSubjectFilter: {
        subjectType: 'user',
        optionalSubjectId: 'mockUserId',
      },
    });
  });

  it('should handle missing optionalSubjectFilter', () => {
    const input = {
      optionalRelation: 'manager',
      resourceType: 'organization',
      optionalResourceId: 'mockPartnerId',
    };

    const result = mapRelationshipFilter(input);

    expect(result).toEqual({
      optionalRelation: 'manager',
      resourceType: 'organization',
      optionalResourceId: 'mockPartnerId',
      optionalResourceIdPrefix: '',
    });
  });

  it('should handle empty strings for optional fields', () => {
    const input = {
      optionalRelation: '',
      resourceType: '',
      optionalResourceId: '',
      optionalSubjectFilter: {
        subjectType: '',
        optionalSubjectId: '',
      },
    };

    const result = mapRelationshipFilter(input);

    expect(result).toEqual({
      optionalRelation: '',
      resourceType: '',
      optionalResourceId: '',
      optionalResourceIdPrefix: '',
      optionalSubjectFilter: {
        subjectType: '',
        optionalSubjectId: '',
      },
    });
  });

  it('should transformed resourceType, subjectType (if any) and optionalRelation', () => {
    const input = {
      optionalRelation: 'MANAGER',
      resourceType: 'ORGANIZATION',
      optionalResourceId: 'mockPartnerId',
      optionalSubjectFilter: {
        subjectType: 'USER',
        optionalSubjectId: 'mockUserId',
      },
    };

    const result = mapRelationshipFilter(input);

    expect(result).toEqual({
      optionalRelation: 'manager',
      resourceType: 'organization',
      optionalResourceId: 'mockPartnerId',
      optionalResourceIdPrefix: '',
      optionalSubjectFilter: {
        subjectType: 'user',
        optionalSubjectId: 'mockUserId',
      },
    });
  });
});

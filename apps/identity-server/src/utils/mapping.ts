import { randomUUID } from 'node:crypto';
import { v1 } from '@authzed/authzed-node';
import {
  Advocate,
  Applicant,
  GCIPUser,
  User as IdentityUser,
  Tenant,
  TenantOptions,
} from '@bybeam/identity-client/types';
import { User } from '@bybeam/identity-client/types';
import { User as CoreUser } from '@bybeam/platform-types';
import { DecodedIdToken, Tenant as FirebaseTenant, UserImportRecord } from 'firebase-admin/auth';

const CoreUserFields = [
  'user.id',
  'user.name',
  'user.email',
  'user.phone',
  'user.validatedEmail',
  'user.createdAt',
];

function mapCoreUserToIdentityService({
  user,
  partnerId,
  tenantId,
  existingIdentityUsers,
  duplicatePhones,
}: {
  user: CoreUser;
  partnerId: string;
  tenantId: string;
  existingIdentityUsers: IdentityUser[];
  duplicatePhones?: string[];
}): {
  user: IdentityUser;
  gcipUser: Omit<GCIPUser, 'id' | 'createdAt' | 'updatedAt' | 'deactivatedAt'>;
  applicant?: Applicant;
  advocate?: Advocate;
  gcipInsert: UserImportRecord;
} {
  const existingIdentityUser = existingIdentityUsers?.find(({ email }) => email === user.email);
  const identityUserId = existingIdentityUser?.id || randomUUID();
  const gcipUid = randomUUID();
  if (duplicatePhones?.includes(user?.phone)) user.phone = undefined;
  const { email, name, phone, admin } = user;
  return {
    user: existingIdentityUser
      ? null
      : {
          id: identityUserId,
          name,
          email,
          ...(phone && { phone }),
          verifiedEmail: !!user?.validatedEmail,
          createdAt: user?.createdAt,
        },
    gcipUser: { uid: gcipUid, tenantId, userId: identityUserId },
    ...(admin
      ? { advocate: { id: admin.id, coreUserId: user.id, userId: identityUserId, partnerId } }
      : { applicant: { id: user.id, userId: identityUserId, partnerId } }),
    gcipInsert: {
      email,
      uid: gcipUid,
      displayName: name,
      emailVerified: !!user?.validatedEmail,
      ...(phone && { phoneNumber: phone }),
    },
  };
}

function mapGCIPToIdentityUser(user: DecodedIdToken): Omit<IdentityUser, 'id' | 'createdAt'> {
  return {
    email: user.email,
    name: user?.displayName ?? user.email,
    ...(user?.phone_number && { phone: user.phone_number }),
    ...(user?.email_verified && { verifiedEmail: user?.email_verified }),
  };
}

function mapIdentityToCoreUser(
  user: Pick<Partial<IdentityUser>, 'id' | 'name' | 'email' | 'phone' | 'verifiedEmail'>,
  partnerId: string,
): Omit<CoreUser, 'displayId' | 'createdAt'> {
  return {
    id: user.id,
    email: user.email,
    partnerId,
    ...(user?.name && { name: user.name }),
    ...(user?.phone && { phone: user.phone }),
    ...(user?.verifiedEmail && { validatedEmail: user?.verifiedEmail }),
  };
}

function mapTenant(tenant: FirebaseTenant): Tenant {
  if (!(tenant?.toJSON instanceof Function)) throw new Error('can not map firebase tenant');
  const { tenantId, displayName, ...options } = tenant.toJSON() as FirebaseTenant;

  return {
    tenantId,
    displayName,
    options: options as TenantOptions,
  };
}

function mapUserUpdateToGCIP(
  user: Pick<Partial<User>, 'name' | 'email' | 'phone' | 'verifiedEmail'>,
) {
  return {
    ...(user?.email && { email: user.email }),
    ...(user?.name && { displayName: user.name }),
    ...(user?.phone !== undefined && { phoneNumber: user.phone }),
    ...(user?.verifiedEmail !== undefined && { emailVerified: user.verifiedEmail }),
  };
}

// biome-ignore lint/suspicious/noExplicitAny: value could be any
function removeEmpty(obj: Record<string, any>): Record<string, any> {
  if (!obj) return obj;
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([_, v]) => !(v === null || v === undefined || v === ''))
      .map(([k, v]) => [k, v instanceof Object && !Array.isArray(v) ? removeEmpty(v) : v]),
  );
}

function mapRelationshipFilter({
  optionalSubjectFilter,
  ...relationshipFilter
}: Partial<v1.RelationshipFilter>): v1.RelationshipFilter {
  return {
    optionalRelation: relationshipFilter.optionalRelation?.toLowerCase() ?? '',
    // resource filter
    resourceType: relationshipFilter.resourceType?.toLowerCase() ?? '',
    optionalResourceId: relationshipFilter.optionalResourceId,
    optionalResourceIdPrefix: '',
    // subject filter
    ...(optionalSubjectFilter && {
      optionalSubjectFilter: {
        ...optionalSubjectFilter,
        subjectType: optionalSubjectFilter?.subjectType.toLowerCase(),
      },
    }),
  };
}

export {
  CoreUserFields,
  mapCoreUserToIdentityService,
  mapGCIPToIdentityUser,
  mapIdentityToCoreUser,
  mapTenant,
  mapUserUpdateToGCIP,
  mapRelationshipFilter,
  removeEmpty,
};

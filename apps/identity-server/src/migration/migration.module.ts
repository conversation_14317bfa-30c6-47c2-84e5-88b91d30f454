import {
  AdvocateEntity,
  ApplicantEntity,
  GCIPUserEntity,
  UserEntity as IdentityUserEntity,
} from '@bybeam/identity-entities';
import {
  ApplicationEntity,
  CaseEntity,
  FulfillmentEntity,
  FundEntity,
  PartnerEntity,
  PaymentEntity,
  UserEntity as PlatformUserEntity,
  ProgramEntity,
  VendorEntity,
  WorkflowEventEntity,
} from '@bybeam/platform-entities';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminModule } from '../admin/admin.module.js';
import { AuthorizationModule } from '../authorization/authorization.module.js';
import { Database } from '../database/database.js';
import { MigrationController } from './migration.controller.js';
import { PermissionsService } from './permissions.service.js';

@Module({
  imports: [
    AdminModule,
    AuthorizationModule,
    ConfigModule,
    TypeOrmModule.forFeature(
      [
        ApplicationEntity,
        CaseEntity,
        FulfillmentEntity,
        FundEntity,
        PartnerEntity,
        PaymentEntity,
        ProgramEntity,
        PlatformUserEntity,
        VendorEntity,
        WorkflowEventEntity,
      ],
      Database.Core,
    ),
    TypeOrmModule.forFeature(
      [AdvocateEntity, ApplicantEntity, GCIPUserEntity, IdentityUserEntity],
      Database.Identity,
    ),
  ],
  controllers: [MigrationController],
  providers: [PermissionsService],
})
export class MigrationModule {}

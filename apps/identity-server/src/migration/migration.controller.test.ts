import { Test, TestingModule } from '@nestjs/testing';
import { describe, expect, it, vi } from 'vitest';
import { MigrationController } from './migration.controller.js';
import { PermissionsService } from './permissions.service.js';

describe('migration', () => {
  describe('migratePermissions', () => {
    it('calls the authorization service and returns the result', async () => {
      const migratePermissions = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        controllers: [MigrationController],
      })
        .useMocker((token) => {
          if (token === PermissionsService)
            return { migratePermissions: migratePermissions.mockResolvedValueOnce('migrated') };
        })
        .compile();
      const result = await module.get<MigrationController>(MigrationController).migratePermissions({
        partnerId: 'mockPartnerId',
        objects: [],
        _useStream: 'useStream',
      });
      expect(result).toEqual('migrated');
      expect(migratePermissions).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        objects: [],
        _useStream: 'useStream',
      });
    });
  });
});

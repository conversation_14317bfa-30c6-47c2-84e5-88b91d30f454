import {
  FixBulkOverridePaymentsRequest,
  FixPayeeRelationsRequest,
  MigrateFiscalPermissionsRequest,
  MigratePermissionsRequest,
} from '@bybeam/identity-client/types';
import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { PermissionsService } from './permissions.service.js';

@Controller('migration')
export class MigrationController {
  constructor(private permissionService: PermissionsService) {}

  @GrpcMethod('IdentityServer', 'migratePermissions')
  async migratePermissions(request: MigratePermissionsRequest) {
    return this.permissionService.migratePermissions(request);
  }

  @GrpcMethod('IdentityServer', 'fixPayeeRelations')
  async fixPayeeRelations(request: FixPayeeRelationsRequest) {
    return this.permissionService.fixPayeeRelations(request);
  }

  @GrpcMethod('IdentityServer', 'fixBulkOverridePaymentsRelations')
  async fixBulkOverridePaymentsRelations(request: FixBulkOverridePaymentsRequest) {
    return this.permissionService.fixBulkOverridePaymentsRelations(request);
  }

  @GrpcMethod('IdentityServer', 'migrateFiscalPermissions')
  async migrateFiscalPermissions(request: MigrateFiscalPermissionsRequest) {
    return this.permissionService.migrateFiscalPermissions(request);
  }
}

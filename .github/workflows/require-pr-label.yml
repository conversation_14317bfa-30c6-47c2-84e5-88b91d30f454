name: Require PRs to be Labeled
on:
  pull_request:
    types: [opened, labeled, unlabeled, synchronize]

jobs:
  check-label:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: mheap/github-action-required-labels@v5
        with:
          mode: minimum
          count: 1
          labels: "app-config, authentication, authorization, bug, ci/cd, cloud-run, comms, elasticsearch-sync,events, fix, data-feature, data-schema, dependencies, features, identity, integrations, partner-support, payments, platform-api, scheduler, security, verification, telemetry, testing, UI"
          add_comment: true
          message: "Pull requests require at least one label before merging."

name: Build, Test, and Coverage
env:
  TURBO_SCM_BASE: "origin/main"
on:
  pull_request:
    # paths-ignore:
      # - ".github/**"
      # - "cloudrun/**"
      # - "dbs/**"
      # - "templates/**"
      # - "turbo/**"
  merge_group:
jobs:
  coverage:
    name: report
    runs-on: github-xlarge
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.10.0
          cache: "pnpm"
      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: 0.5.15
          activate-environment: true
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install modules
        run: pnpm install
      - name: Build apps and packages
        run: pnpm build:ci
      - name: Check for partner-portal changes
        id: check-partner-portal
        run: |
          if git diff origin/main...HEAD --compact-summary | grep -q "partner-portal/"; then
            echo "changes=true" >> $GITHUB_OUTPUT
            echo "Partner portal changes detected"
          else
            echo "changes=false" >> $GITHUB_OUTPUT
            echo "No partner portal changes detected"
          fi
      - name: Install playwright dependencies
        if: ${{ steps.check-partner-portal.outputs.changes == 'true' }}
        run: pnpm exec playwright install chromium --with-deps
      - name: Run partner portal tests
        if: ${{ steps.check-partner-portal.outputs.changes == 'true' }}
        run: pnpm test:partner-portal:ci
      - name: Run affected tests
        run: pnpm test:coverage:ci
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
